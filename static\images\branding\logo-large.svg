<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 120" width="300" height="120">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#D4AF37;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8860B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="navyGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="300" height="120" rx="15" fill="url(#navyGradient)" filter="url(#shadow)"/>
  
  <!-- Justice Scale Icon -->
  <g transform="translate(30, 30)">
    <!-- Scale Base -->
    <rect x="25" y="45" width="8" height="15" fill="url(#goldGradient)"/>
    <rect x="20" y="60" width="18" height="4" fill="url(#goldGradient)"/>
    
    <!-- Scale Beam -->
    <rect x="10" y="32" width="40" height="4" fill="url(#goldGradient)"/>
    
    <!-- Left Scale Pan -->
    <ellipse cx="16" cy="40" rx="12" ry="4" fill="none" stroke="url(#goldGradient)" stroke-width="3"/>
    <line x1="4" y1="36" x2="28" y2="36" stroke="url(#goldGradient)" stroke-width="2"/>
    
    <!-- Right Scale Pan -->
    <ellipse cx="44" cy="40" rx="12" ry="4" fill="none" stroke="url(#goldGradient)" stroke-width="3"/>
    <line x1="32" y1="36" x2="56" y2="36" stroke="url(#goldGradient)" stroke-width="2"/>
    
    <!-- Scale Chains -->
    <line x1="16" y1="32" x2="16" y2="36" stroke="url(#goldGradient)" stroke-width="2"/>
    <line x1="44" y1="32" x2="44" y2="36" stroke="url(#goldGradient)" stroke-width="2"/>
    
    <!-- Top Decoration -->
    <circle cx="30" cy="15" r="6" fill="none" stroke="url(#goldGradient)" stroke-width="3"/>
    <line x1="30" y1="21" x2="30" y2="32" stroke="url(#goldGradient)" stroke-width="3"/>
    
    <!-- Decorative Elements -->
    <circle cx="10" cy="10" r="2" fill="url(#goldGradient)" opacity="0.6"/>
    <circle cx="50" cy="10" r="2" fill="url(#goldGradient)" opacity="0.6"/>
    <circle cx="5" cy="25" r="1.5" fill="url(#goldGradient)" opacity="0.4"/>
    <circle cx="55" cy="25" r="1.5" fill="url(#goldGradient)" opacity="0.4"/>
  </g>
  
  <!-- Arabic Text -->
  <text x="110" y="45" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="url(#goldGradient)">
    قانوني
  </text>
  <text x="110" y="70" font-family="Arial, sans-serif" font-size="16" fill="#ffffff" opacity="0.9">
    موقع قانوني متخصص
  </text>
  <text x="110" y="90" font-family="Arial, sans-serif" font-size="12" fill="#ffffff" opacity="0.7">
    Legal Expert Platform
  </text>
  
  <!-- Decorative Border -->
  <rect x="5" y="5" width="290" height="110" rx="10" fill="none" stroke="url(#goldGradient)" stroke-width="1" opacity="0.3"/>
  
  <!-- Corner Decorations -->
  <circle cx="270" cy="25" r="3" fill="url(#goldGradient)" opacity="0.6"/>
  <circle cx="280" cy="40" r="2" fill="url(#goldGradient)" opacity="0.4"/>
  <circle cx="265" cy="55" r="2" fill="url(#goldGradient)" opacity="0.3"/>
  <circle cx="275" cy="70" r="1.5" fill="url(#goldGradient)" opacity="0.2"/>
</svg>
