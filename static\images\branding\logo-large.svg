<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 350 130" width="350" height="130">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#D4AF37;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8860B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="navyGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Outer Circle -->
  <circle cx="65" cy="65" r="55" fill="url(#navyGradient)" filter="url(#shadow)"/>
  <circle cx="65" cy="65" r="48" fill="none" stroke="url(#goldGradient)" stroke-width="4"/>

  <!-- Inner Symbol - Stylized Gavel -->
  <g transform="translate(65, 65)">
    <!-- Gavel Handle -->
    <rect x="-4" y="-30" width="8" height="40" fill="url(#goldGradient)" rx="4"/>

    <!-- Gavel Head -->
    <rect x="-16" y="-36" width="32" height="12" fill="url(#goldGradient)" rx="6"/>

    <!-- Base -->
    <ellipse cx="0" cy="16" rx="24" ry="6" fill="url(#goldGradient)" opacity="0.8"/>

    <!-- Decorative Stars -->
    <polygon points="0,-16 2,-12 6,-12 2,-8 4,-4 0,-6 -4,-4 -2,-8 -6,-12 -2,-12"
             fill="url(#goldGradient)" opacity="0.6"/>

    <!-- Side decorations -->
    <circle cx="-25" cy="-15" r="3" fill="url(#goldGradient)" opacity="0.5"/>
    <circle cx="25" cy="-15" r="3" fill="url(#goldGradient)" opacity="0.5"/>
    <circle cx="-20" cy="5" r="2" fill="url(#goldGradient)" opacity="0.4"/>
    <circle cx="20" cy="5" r="2" fill="url(#goldGradient)" opacity="0.4"/>
  </g>

  <!-- Arabic Text -->
  <text x="140" y="55" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="url(#navyGradient)">
    قانوني
  </text>
  <text x="140" y="80" font-family="Arial, sans-serif" font-size="18" fill="url(#navyGradient)" opacity="0.8">
    موقع قانوني متخصص
  </text>
  <text x="140" y="100" font-family="Arial, sans-serif" font-size="14" fill="url(#navyGradient)" opacity="0.6">
    Legal Expert Platform
  </text>

  <!-- Decorative Border -->
  <rect x="5" y="5" width="290" height="110" rx="10" fill="none" stroke="url(#goldGradient)" stroke-width="1" opacity="0.3"/>

  <!-- Corner Decorations -->
  <circle cx="270" cy="25" r="3" fill="url(#goldGradient)" opacity="0.6"/>
  <circle cx="280" cy="40" r="2" fill="url(#goldGradient)" opacity="0.4"/>
  <circle cx="265" cy="55" r="2" fill="url(#goldGradient)" opacity="0.3"/>
  <circle cx="275" cy="70" r="1.5" fill="url(#goldGradient)" opacity="0.2"/>
</svg>
