<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 60" width="200" height="60">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#D4AF37;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFD700;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="navyGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Modern Hexagon Shape -->
  <polygon points="15,30 25,15 35,15 45,30 35,45 25,45" fill="url(#navyGradient)"/>
  
  <!-- Inner Icon - Book with Scales -->
  <g transform="translate(30, 30)">
    <!-- Book -->
    <rect x="-8" y="-8" width="16" height="12" rx="1" fill="url(#goldGradient)"/>
    <line x1="-8" y1="-2" x2="8" y2="-2" stroke="url(#navyGradient)" stroke-width="1"/>
    <line x1="-8" y1="2" x2="8" y2="2" stroke="url(#navyGradient)" stroke-width="1"/>
    
    <!-- Small scales on top -->
    <line x1="-4" y1="-12" x2="4" y2="-12" stroke="url(#goldGradient)" stroke-width="1.5"/>
    <circle cx="-3" cy="-10" r="1.5" fill="none" stroke="url(#goldGradient)" stroke-width="1"/>
    <circle cx="3" cy="-10" r="1.5" fill="none" stroke="url(#goldGradient)" stroke-width="1"/>
  </g>
  
  <!-- Arabic Text -->
  <text x="60" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="url(#navyGradient)">
    قانوني
  </text>
  <text x="60" y="42" font-family="Arial, sans-serif" font-size="11" fill="url(#navyGradient)" opacity="0.7">
    موقع قانوني متخصص
  </text>
  <text x="60" y="55" font-family="Arial, sans-serif" font-size="8" fill="url(#navyGradient)" opacity="0.5">
    Legal Expert Platform
  </text>
  
  <!-- Decorative Line -->
  <line x1="55" y1="30" x2="190" y2="30" stroke="url(#goldGradient)" stroke-width="2" opacity="0.3"/>
</svg>
