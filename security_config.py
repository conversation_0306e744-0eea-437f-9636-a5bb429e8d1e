"""
Security Configuration for Legal Website
Comprehensive security settings and utilities
"""

import os
import secrets
import re
from datetime import timedelta

class SecurityConfig:
    """Security configuration class"""
    
    # Secret Keys
    SECRET_KEY = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
    WTF_CSRF_SECRET_KEY = os.environ.get('WTF_CSRF_SECRET_KEY') or secrets.token_hex(32)
    
    # Session Security
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    SESSION_COOKIE_SECURE = True  # HTTPS only
    SESSION_COOKIE_HTTPONLY = True  # No JavaScript access
    SESSION_COOKIE_SAMESITE = 'Lax'  # CSRF protection
    
    # Password Security
    PASSWORD_MIN_LENGTH = 8
    PASSWORD_MAX_LENGTH = 128
    PASSWORD_REQUIRE_UPPERCASE = True
    PASSWORD_REQUIRE_LOWERCASE = True
    PASSWORD_REQUIRE_NUMBERS = True
    PASSWORD_REQUIRE_SPECIAL = True
    
    # Account Security
    MAX_LOGIN_ATTEMPTS = 5
    ACCOUNT_LOCKOUT_DURATION = timedelta(minutes=30)
    PASSWORD_RESET_TIMEOUT = timedelta(hours=1)
    
    # Rate Limiting
    RATE_LIMIT_STORAGE_URL = os.environ.get('RATE_LIMIT_STORAGE_URL', 'memory://')
    DEFAULT_RATE_LIMITS = ["200 per day", "50 per hour"]
    LOGIN_RATE_LIMIT = "5 per minute"
    API_RATE_LIMIT = "100 per hour"
    
    # File Upload Security
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'webp'}
    ALLOWED_MIME_TYPES = {
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp'
    }
    
    # Content Security Policy
    CSP_DIRECTIVES = {
        'default-src': "'self'",
        'script-src': "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
        'style-src': "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://fonts.googleapis.com",
        'img-src': "'self' data: https:",
        'font-src': "'self' https://fonts.gstatic.com https://cdn.jsdelivr.net",
        'connect-src': "'self'",
        'frame-ancestors': "'none'",
        'base-uri': "'self'",
        'object-src': "'none'",
        'media-src': "'self'",
        'form-action': "'self'",
        'upgrade-insecure-requests': True
    }
    
    # Security Headers
    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'geolocation=(), camera=(), microphone=()',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
    }
    
    # Input Validation
    ALLOWED_HTML_TAGS = [
        'p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'ul', 'ol', 'li', 'blockquote', 'a', 'img', 'div', 'span'
    ]
    
    ALLOWED_HTML_ATTRIBUTES = {
        'a': ['href', 'title', 'target'],
        'img': ['src', 'alt', 'title', 'width', 'height'],
        'div': ['class'],
        'span': ['class']
    }
    
    # Suspicious Patterns
    SUSPICIOUS_PATTERNS = [
        r'<script[^>]*>',
        r'javascript:',
        r'vbscript:',
        r'onload\s*=',
        r'onerror\s*=',
        r'onclick\s*=',
        r'onmouseover\s*=',
        r'eval\s*\(',
        r'document\.cookie',
        r'document\.write',
        r'window\.location',
        r'<iframe[^>]*>',
        r'<object[^>]*>',
        r'<embed[^>]*>',
        r'<form[^>]*>',
        r'<input[^>]*>',
        r'<meta[^>]*>',
        r'<link[^>]*>',
        r'<style[^>]*>',
        r'expression\s*\(',
        r'url\s*\(',
        r'@import',
        r'<!--.*-->',
        r'<!\[CDATA\[',
        r'\]\]>',
        r'&\w+;'
    ]
    
    # SQL Injection Patterns
    SQL_INJECTION_PATTERNS = [
        r'(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)',
        r'(\b(OR|AND)\s+\d+\s*=\s*\d+)',
        r'(\b(OR|AND)\s+[\'"][^\'"]*[\'"])',
        r'(--|#|/\*|\*/)',
        r'(\bxp_\w+)',
        r'(\bsp_\w+)',
        r'(\bUNION\s+SELECT)',
        r'(\bINTO\s+OUTFILE)',
        r'(\bLOAD_FILE)',
        r'(\bCHAR\s*\()',
        r'(\bCONCAT\s*\()',
        r'(\bSUBSTRING\s*\()',
        r'(\bASCII\s*\()',
        r'(\bHEX\s*\()',
        r'(\bUNHEX\s*\()',
        r'(\bBENCHMARK\s*\()',
        r'(\bSLEEP\s*\()',
        r'(\bWAITFOR\s+DELAY)',
        r'(\bIF\s*\()',
        r'(\bCASE\s+WHEN)',
        r'(\bEXISTS\s*\()',
        r'(\bHAVING\s+)',
        r'(\bGROUP\s+BY)',
        r'(\bORDER\s+BY)',
        r'(\bLIMIT\s+)',
        r'(\bOFFSET\s+)'
    ]
    
    # XSS Patterns
    XSS_PATTERNS = [
        r'<script[^>]*>.*?</script>',
        r'<iframe[^>]*>.*?</iframe>',
        r'<object[^>]*>.*?</object>',
        r'<embed[^>]*>.*?</embed>',
        r'<applet[^>]*>.*?</applet>',
        r'<meta[^>]*>',
        r'<link[^>]*>',
        r'<style[^>]*>.*?</style>',
        r'javascript:',
        r'vbscript:',
        r'data:text/html',
        r'on\w+\s*=',
        r'expression\s*\(',
        r'url\s*\(',
        r'@import',
        r'eval\s*\(',
        r'setTimeout\s*\(',
        r'setInterval\s*\(',
        r'Function\s*\(',
        r'document\.',
        r'window\.',
        r'location\.',
        r'navigator\.',
        r'history\.',
        r'screen\.',
        r'alert\s*\(',
        r'confirm\s*\(',
        r'prompt\s*\('
    ]
    
    # Logging Configuration
    SECURITY_LOG_FILE = 'logs/security.log'
    SECURITY_LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    SECURITY_LOG_MAX_BYTES = 10 * 1024 * 1024  # 10MB
    SECURITY_LOG_BACKUP_COUNT = 5
    
    # Monitoring
    FAILED_LOGIN_THRESHOLD = 10  # Alert after 10 failed logins
    SUSPICIOUS_ACTIVITY_THRESHOLD = 5  # Alert after 5 suspicious activities
    MONITORING_TIME_WINDOW = timedelta(minutes=15)
    
    # IP Whitelist/Blacklist
    ADMIN_IP_WHITELIST = os.environ.get('ADMIN_IP_WHITELIST', '').split(',')
    IP_BLACKLIST = []
    
    # Database Security
    DATABASE_ENCRYPTION_KEY = os.environ.get('DATABASE_ENCRYPTION_KEY') or secrets.token_hex(32)
    BACKUP_ENCRYPTION_KEY = os.environ.get('BACKUP_ENCRYPTION_KEY') or secrets.token_hex(32)
    
    # API Security
    API_KEY_LENGTH = 32
    API_TOKEN_EXPIRY = timedelta(hours=24)
    
    # Email Security
    EMAIL_RATE_LIMIT = "5 per hour"
    EMAIL_VERIFICATION_TIMEOUT = timedelta(hours=24)
    
    # Two-Factor Authentication
    TOTP_SECRET_LENGTH = 32
    TOTP_VALIDITY_WINDOW = 1
    BACKUP_CODES_COUNT = 10
    
    @staticmethod
    def validate_password(password):
        """Validate password strength"""
        if len(password) < SecurityConfig.PASSWORD_MIN_LENGTH:
            return False, f"كلمة المرور يجب أن تكون {SecurityConfig.PASSWORD_MIN_LENGTH} أحرف على الأقل"
        
        if len(password) > SecurityConfig.PASSWORD_MAX_LENGTH:
            return False, f"كلمة المرور يجب أن تكون أقل من {SecurityConfig.PASSWORD_MAX_LENGTH} حرف"
        
        if SecurityConfig.PASSWORD_REQUIRE_UPPERCASE and not re.search(r'[A-Z]', password):
            return False, "كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل"
        
        if SecurityConfig.PASSWORD_REQUIRE_LOWERCASE and not re.search(r'[a-z]', password):
            return False, "كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل"
        
        if SecurityConfig.PASSWORD_REQUIRE_NUMBERS and not re.search(r'[0-9]', password):
            return False, "كلمة المرور يجب أن تحتوي على رقم واحد على الأقل"
        
        if SecurityConfig.PASSWORD_REQUIRE_SPECIAL and not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            return False, "كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل"
        
        return True, "كلمة مرور قوية"
    
    @staticmethod
    def is_suspicious_input(text):
        """Check if input contains suspicious patterns"""
        if not text:
            return False
        
        text_lower = text.lower()
        
        # Check for suspicious patterns
        for pattern in SecurityConfig.SUSPICIOUS_PATTERNS:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True
        
        # Check for SQL injection patterns
        for pattern in SecurityConfig.SQL_INJECTION_PATTERNS:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True
        
        # Check for XSS patterns
        for pattern in SecurityConfig.XSS_PATTERNS:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True
        
        return False
    
    @staticmethod
    def validate_email(email):
        """Validate email format"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def validate_phone(phone):
        """Validate phone number format"""
        # Moroccan phone number pattern
        pattern = r'^(\+212|0)[5-7][0-9]{8}$'
        return re.match(pattern, phone) is not None
    
    @staticmethod
    def generate_secure_token(length=32):
        """Generate secure random token"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def generate_csrf_token():
        """Generate CSRF token"""
        return secrets.token_hex(16)

# Environment-specific configurations
class DevelopmentSecurityConfig(SecurityConfig):
    """Development security configuration"""
    SESSION_COOKIE_SECURE = False  # Allow HTTP in development
    SECURITY_HEADERS = {
        **SecurityConfig.SECURITY_HEADERS,
        'Strict-Transport-Security': None  # Disable HSTS in development
    }

class ProductionSecurityConfig(SecurityConfig):
    """Production security configuration"""
    SESSION_COOKIE_SECURE = True  # Force HTTPS
    FORCE_HTTPS = True
    
    # Stricter CSP in production
    CSP_DIRECTIVES = {
        **SecurityConfig.CSP_DIRECTIVES,
        'script-src': "'self'",  # No inline scripts
        'style-src': "'self'",   # No inline styles
    }
    
    # Stricter rate limits
    DEFAULT_RATE_LIMITS = ["100 per day", "20 per hour"]
    LOGIN_RATE_LIMIT = "3 per minute"

# Security utilities
def get_security_config(environment='development'):
    """Get security configuration based on environment"""
    if environment == 'production':
        return ProductionSecurityConfig
    else:
        return DevelopmentSecurityConfig
