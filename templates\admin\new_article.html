{% extends "admin/base.html" %}

{% block title %}مقال جديد{% endblock %}
{% block page_title %}إنشاء مقال جديد{% endblock %}

{% block page_actions %}
<a href="{{ url_for('admin_articles') }}" class="btn btn-outline-secondary">
    <i class="bi bi-arrow-right me-1"></i>
    العودة للمقالات
</a>
{% endblock %}

{% block content %}
<form method="POST" id="articleForm">
    <div class="row">
        <div class="col-lg-8">
            <!-- Basic Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        المعلومات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="title_ar" class="form-label fw-bold">العنوان بالعربية *</label>
                        <input type="text" class="form-control" id="title_ar" name="title_ar" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="title" class="form-label fw-bold">العنوان بالإنجليزية</label>
                        <input type="text" class="form-control" id="title" name="title">
                        <div class="form-text">اختياري - للاستخدام في URLs</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="excerpt" class="form-label fw-bold">الملخص *</label>
                        <textarea class="form-control" id="excerpt" name="excerpt" rows="3" required></textarea>
                        <div class="form-text">ملخص قصير للمقال (يظهر في قوائم المقالات)</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label fw-bold">التصنيف *</label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">اختر التصنيف</option>
                                {% for cat in categories %}
                                <option value="{{ cat }}">{{ cat }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="tags" class="form-label fw-bold">العلامات</label>
                            <input type="text" class="form-control" id="tags" name="tags" 
                                   placeholder="علامة1, علامة2, علامة3">
                            <div class="form-text">افصل بين العلامات بفاصلة</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Content -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-file-text me-2"></i>
                        محتوى المقال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="content" class="form-label fw-bold">المحتوى *</label>
                        <textarea class="form-control" id="content" name="content" rows="15" required></textarea>
                        <div class="form-text">يمكنك استخدام HTML للتنسيق</div>
                    </div>
                </div>
            </div>
            
            <!-- SEO Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-search me-2"></i>
                        إعدادات SEO
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="meta_title" class="form-label fw-bold">عنوان SEO</label>
                        <input type="text" class="form-control" id="meta_title" name="meta_title">
                        <div class="form-text">إذا ترك فارغاً، سيتم استخدام العنوان الأساسي</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="meta_description" class="form-label fw-bold">وصف SEO</label>
                        <textarea class="form-control" id="meta_description" name="meta_description" rows="2"></textarea>
                        <div class="form-text">وصف قصير للمقال (يظهر في نتائج البحث)</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Publish Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-gear me-2"></i>
                        إعدادات النشر
                    </h5>
                </div>
                <div class="card-body">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_published" name="is_published">
                        <label class="form-check-label fw-bold" for="is_published">
                            نشر المقال
                        </label>
                        <div class="form-text">إذا لم يتم تحديده، سيتم حفظه كمسودة</div>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured">
                        <label class="form-check-label fw-bold" for="is_featured">
                            مقال مميز
                        </label>
                        <div class="form-text">سيظهر في قسم المقالات المميزة</div>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary-custom btn-lg">
                            <i class="bi bi-check-circle me-2"></i>
                            حفظ المقال
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="previewArticle()">
                            <i class="bi bi-eye me-2"></i>
                            معاينة
                        </button>
                        <a href="{{ url_for('admin_articles') }}" class="btn btn-outline-danger">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Tips -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="bi bi-lightbulb me-2"></i>
                        نصائح للكتابة
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled small">
                        <li class="mb-2">
                            <i class="bi bi-check text-success me-1"></i>
                            استخدم عناوين واضحة ومفهومة
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check text-success me-1"></i>
                            اكتب ملخصاً جذاباً للمقال
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check text-success me-1"></i>
                            استخدم العلامات لتسهيل البحث
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check text-success me-1"></i>
                            راجع المحتوى قبل النشر
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة المقال</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Preview content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function previewArticle() {
    const title = document.getElementById('title_ar').value;
    const content = document.getElementById('content').value;
    const excerpt = document.getElementById('excerpt').value;
    const category = document.getElementById('category').value;
    
    if (!title || !content) {
        alert('يرجى ملء العنوان والمحتوى أولاً');
        return;
    }
    
    const previewHTML = `
        <div class="article-preview">
            <div class="mb-3">
                <span class="badge bg-primary">${category}</span>
            </div>
            <h1 class="display-6 fw-bold text-primary mb-3">${title}</h1>
            <p class="lead text-muted mb-4">${excerpt}</p>
            <div class="article-content">
                ${content}
            </div>
        </div>
    `;
    
    document.getElementById('previewContent').innerHTML = previewHTML;
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// Auto-save functionality
let autoSaveTimer;
function autoSave() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(() => {
        const formData = new FormData(document.getElementById('articleForm'));
        // Here you could implement auto-save to localStorage or server
        console.log('Auto-saving...');
    }, 30000); // Auto-save every 30 seconds
}

// Add event listeners for auto-save
document.querySelectorAll('#articleForm input, #articleForm textarea, #articleForm select').forEach(element => {
    element.addEventListener('input', autoSave);
});

// Character counter for excerpt
const excerptTextarea = document.getElementById('excerpt');
const excerptCounter = document.createElement('div');
excerptCounter.className = 'form-text text-end';
excerptTextarea.parentNode.appendChild(excerptCounter);

excerptTextarea.addEventListener('input', function() {
    const length = this.value.length;
    excerptCounter.textContent = `${length} حرف`;
    
    if (length > 200) {
        excerptCounter.className = 'form-text text-end text-warning';
        excerptCounter.textContent += ' - طويل جداً';
    } else if (length < 50) {
        excerptCounter.className = 'form-text text-end text-muted';
        excerptCounter.textContent += ' - قصير';
    } else {
        excerptCounter.className = 'form-text text-end text-success';
        excerptCounter.textContent += ' - مناسب';
    }
});
</script>
{% endblock %}
