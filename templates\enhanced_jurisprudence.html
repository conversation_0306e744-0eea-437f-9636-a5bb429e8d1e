{% extends "enhanced_base.html" %}

{% block title %}الاجتهادات المغربية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb" class="bg-light py-2">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
            <li class="breadcrumb-item active">الاجتهادات المغربية</li>
        </ol>
    </div>
</nav>
{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="bg-primary-custom text-white py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="display-5 fw-bold mb-2">الاجتهادات المغربية</h1>
                <p class="lead mb-0">مجموعة شاملة من الاجتهادات والأحكام القضائية المغربية</p>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- Search Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-8">
                            <label for="search" class="form-label fw-bold">البحث في الاجتهادات</label>
                            <input type="text" class="form-control" id="search" name="search" 
                                   value="{{ current_search }}" placeholder="ابحث في العناوين والملخصات...">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary-custom w-100">
                                <i class="bi bi-search me-1"></i>بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Info -->
    {% if current_search %}
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                عرض {{ cases|length }} نتيجة للبحث عن "{{ current_search }}"
                <a href="{{ url_for('jurisprudence') }}" class="btn btn-sm btn-outline-primary ms-3">
                    <i class="bi bi-x-circle me-1"></i>إزالة البحث
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Jurisprudence Cases -->
    {% if cases %}
    <div class="row">
        {% for case in cases %}
        <div class="col-lg-6 mb-4">
            <div class="card h-100 border-0 shadow-sm card-hover">
                <div class="card-header bg-light border-0">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <span class="badge bg-primary-custom">{{ case.category }}</span>
                            <span class="badge bg-gold ms-2">{{ case.court }}</span>
                        </div>
                        <small class="text-muted">{{ case.date }}</small>
                    </div>
                </div>
                
                <div class="card-body">
                    <h5 class="card-title fw-bold text-primary-custom mb-3">
                        {{ case.title }}
                    </h5>
                    
                    <div class="mb-3">
                        <strong class="text-dark">رقم القضية:</strong>
                        <span class="text-muted">{{ case.case_number }}</span>
                    </div>
                    
                    <div class="mb-3">
                        <strong class="text-dark">الملخص:</strong>
                        <p class="text-muted mt-1">{{ case.summary }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <strong class="text-dark">المبدأ القانوني:</strong>
                        <p class="text-primary-custom mt-1 fw-bold">{{ case.legal_principle }}</p>
                    </div>
                </div>
                
                <div class="card-footer bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <i class="bi bi-calendar3 me-1"></i>
                            تاريخ القرار: {{ case.date }}
                        </small>
                        <button class="btn btn-sm btn-outline-primary" onclick="copyCase('{{ case.case_number }}')">
                            <i class="bi bi-copy me-1"></i>نسخ
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    {% else %}
    <!-- No Cases Found -->
    <div class="text-center py-5">
        <i class="bi bi-bank display-1 text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد اجتهادات</h4>
        {% if current_search %}
        <p class="text-muted">لم يتم العثور على اجتهادات تطابق معايير البحث</p>
        <a href="{{ url_for('jurisprudence') }}" class="btn btn-primary-custom">
            <i class="bi bi-arrow-right me-2"></i>
            عرض جميع الاجتهادات
        </a>
        {% else %}
        <p class="text-muted">تحقق مرة أخرى لاحقاً للاطلاع على الاجتهادات الجديدة</p>
        <a href="{{ url_for('index') }}" class="btn btn-primary-custom">
            <i class="bi bi-house me-2"></i>
            العودة للرئيسية
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- Info Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h3 class="fw-bold text-primary-custom mb-3">عن الاجتهادات المغربية</h3>
                <p class="lead text-muted mb-4">
                    تشكل الاجتهادات القضائية المغربية مصدراً مهماً من مصادر القانون، حيث تساهم في تفسير النصوص القانونية وتطبيقها على الحالات العملية.
                </p>
                
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="bi bi-shield-check text-primary-custom display-6 mb-2"></i>
                                <h6 class="fw-bold">موثوقية عالية</h6>
                                <small class="text-muted">جميع الاجتهادات مؤكدة ومراجعة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="bi bi-search text-primary-custom display-6 mb-2"></i>
                                <h6 class="fw-bold">بحث متقدم</h6>
                                <small class="text-muted">ابحث بسهولة في قاعدة البيانات</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card border-0 shadow-sm">
                            <div class="card-body text-center">
                                <i class="bi bi-arrow-clockwise text-primary-custom display-6 mb-2"></i>
                                <h6 class="fw-bold">تحديث مستمر</h6>
                                <small class="text-muted">إضافة الاجتهادات الجديدة باستمرار</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function copyCase(caseNumber) {
    const caseInfo = `رقم القضية: ${caseNumber}`;
    navigator.clipboard.writeText(caseInfo).then(() => {
        alert('تم نسخ معلومات القضية بنجاح!');
    }).catch(() => {
        alert('فشل في نسخ المعلومات');
    });
}
</script>
{% endblock %}
