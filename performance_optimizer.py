"""
Advanced Performance Optimization System
Real-time performance monitoring and optimization
"""

import time
import psutil
import gc
import threading
import json
import os
from datetime import datetime, timedelta
from collections import defaultdict, deque
import logging
from functools import wraps
import cProfile
import pstats
import io

class PerformanceOptimizer:
    """Comprehensive performance optimization system"""
    
    def __init__(self):
        self.metrics = {
            'response_times': deque(maxlen=1000),
            'memory_usage': deque(maxlen=1000),
            'cpu_usage': deque(maxlen=1000),
            'database_queries': deque(maxlen=1000),
            'cache_hits': deque(maxlen=1000),
            'error_rates': deque(maxlen=1000)
        }
        
        self.performance_cache = {}
        self.cache_stats = {'hits': 0, 'misses': 0}
        
        # Performance thresholds
        self.thresholds = {
            'response_time': 2.0,  # seconds
            'memory_usage': 80,    # percentage
            'cpu_usage': 75,       # percentage
            'error_rate': 5,       # percentage
            'cache_hit_rate': 80   # percentage
        }
        
        # Optimization settings
        self.optimizations = {
            'enable_caching': True,
            'enable_compression': True,
            'enable_minification': True,
            'enable_lazy_loading': True,
            'enable_database_optimization': True,
            'enable_memory_optimization': True
        }
        
        # Setup logging
        self.setup_logging()
        
        # Start monitoring
        self.start_monitoring()
    
    def setup_logging(self):
        """Setup performance logging"""
        log_dir = 'logs'
        os.makedirs(log_dir, exist_ok=True)
        
        self.logger = logging.getLogger('performance_optimizer')
        self.logger.setLevel(logging.INFO)
        
        # File handler
        file_handler = logging.FileHandler(f'{log_dir}/performance.log')
        file_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def monitor_performance(self, func):
        """Decorator to monitor function performance"""
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            start_memory = psutil.Process().memory_info().rss
            
            try:
                result = func(*args, **kwargs)
                
                # Record successful execution
                end_time = time.time()
                end_memory = psutil.Process().memory_info().rss
                
                execution_time = end_time - start_time
                memory_delta = end_memory - start_memory
                
                # Store metrics
                self.record_performance_metric('response_times', execution_time)
                self.record_performance_metric('memory_usage', memory_delta)
                
                # Check for performance issues
                if execution_time > self.thresholds['response_time']:
                    self.logger.warning(f"Slow function: {func.__name__} took {execution_time:.2f}s")
                
                return result
                
            except Exception as e:
                # Record error
                self.record_performance_metric('error_rates', 1)
                self.logger.error(f"Function {func.__name__} failed: {e}")
                raise
        
        return wrapper
    
    def record_performance_metric(self, metric_type, value):
        """Record performance metric"""
        timestamp = datetime.now()
        
        metric_data = {
            'timestamp': timestamp.isoformat(),
            'value': value
        }
        
        self.metrics[metric_type].append(metric_data)
    
    def get_performance_stats(self):
        """Get current performance statistics"""
        stats = {}
        
        for metric_type, data in self.metrics.items():
            if data:
                values = [item['value'] for item in data]
                stats[metric_type] = {
                    'current': values[-1] if values else 0,
                    'average': sum(values) / len(values),
                    'min': min(values),
                    'max': max(values),
                    'count': len(values)
                }
            else:
                stats[metric_type] = {
                    'current': 0,
                    'average': 0,
                    'min': 0,
                    'max': 0,
                    'count': 0
                }
        
        # Add system metrics
        stats['system'] = {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent,
            'network_io': psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {}
        }
        
        # Add cache statistics
        total_cache_requests = self.cache_stats['hits'] + self.cache_stats['misses']
        cache_hit_rate = (self.cache_stats['hits'] / total_cache_requests * 100) if total_cache_requests > 0 else 0
        
        stats['cache'] = {
            'hit_rate': cache_hit_rate,
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'size': len(self.performance_cache)
        }
        
        return stats
    
    def optimize_memory(self):
        """Optimize memory usage"""
        if not self.optimizations['enable_memory_optimization']:
            return
        
        initial_memory = psutil.Process().memory_info().rss
        
        # Force garbage collection
        gc.collect()
        
        # Clear old cache entries
        self.cleanup_cache()
        
        # Clear old metrics
        self.cleanup_old_metrics()
        
        final_memory = psutil.Process().memory_info().rss
        memory_freed = initial_memory - final_memory
        
        if memory_freed > 0:
            self.logger.info(f"Memory optimization freed {memory_freed / (1024*1024):.2f} MB")
    
    def cleanup_cache(self):
        """Clean up old cache entries"""
        current_time = time.time()
        cache_ttl = 3600  # 1 hour
        
        expired_keys = []
        for key, (value, timestamp) in self.performance_cache.items():
            if current_time - timestamp > cache_ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            del self.performance_cache[key]
        
        if expired_keys:
            self.logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def cleanup_old_metrics(self):
        """Clean up old performance metrics"""
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        for metric_type, data in self.metrics.items():
            # Keep only last 24 hours of data
            while data and datetime.fromisoformat(data[0]['timestamp']) < cutoff_time:
                data.popleft()
    
    def cache_get(self, key):
        """Get value from performance cache"""
        if not self.optimizations['enable_caching']:
            return None
        
        if key in self.performance_cache:
            value, timestamp = self.performance_cache[key]
            # Check if cache entry is still valid (1 hour TTL)
            if time.time() - timestamp < 3600:
                self.cache_stats['hits'] += 1
                return value
            else:
                # Remove expired entry
                del self.performance_cache[key]
        
        self.cache_stats['misses'] += 1
        return None
    
    def cache_set(self, key, value):
        """Set value in performance cache"""
        if not self.optimizations['enable_caching']:
            return
        
        self.performance_cache[key] = (value, time.time())
        
        # Limit cache size
        if len(self.performance_cache) > 1000:
            # Remove oldest entries
            oldest_keys = sorted(
                self.performance_cache.keys(),
                key=lambda k: self.performance_cache[k][1]
            )[:100]
            
            for key in oldest_keys:
                del self.performance_cache[key]
    
    def profile_function(self, func, *args, **kwargs):
        """Profile function execution"""
        profiler = cProfile.Profile()
        profiler.enable()
        
        try:
            result = func(*args, **kwargs)
        finally:
            profiler.disable()
        
        # Get profiling results
        s = io.StringIO()
        ps = pstats.Stats(profiler, stream=s)
        ps.sort_stats('cumulative')
        ps.print_stats(20)  # Top 20 functions
        
        profile_output = s.getvalue()
        
        # Log profiling results
        self.logger.info(f"Profiling results for {func.__name__}:\n{profile_output}")
        
        return result, profile_output
    
    def optimize_database_queries(self, query_log):
        """Analyze and optimize database queries"""
        if not self.optimizations['enable_database_optimization']:
            return []
        
        optimizations = []
        
        # Analyze query patterns
        query_stats = defaultdict(list)
        
        for query_data in query_log:
            query = query_data.get('query', '')
            execution_time = query_data.get('execution_time', 0)
            
            # Normalize query for analysis
            normalized_query = self.normalize_query(query)
            query_stats[normalized_query].append(execution_time)
        
        # Find slow queries
        for query, times in query_stats.items():
            avg_time = sum(times) / len(times)
            if avg_time > 0.1:  # Queries taking more than 100ms
                optimizations.append({
                    'type': 'slow_query',
                    'query': query,
                    'average_time': avg_time,
                    'occurrences': len(times),
                    'suggestion': self.suggest_query_optimization(query)
                })
        
        return optimizations
    
    def normalize_query(self, query):
        """Normalize SQL query for analysis"""
        # Remove specific values and normalize whitespace
        import re
        
        # Replace string literals
        query = re.sub(r"'[^']*'", "'?'", query)
        
        # Replace numeric literals
        query = re.sub(r'\b\d+\b', '?', query)
        
        # Normalize whitespace
        query = ' '.join(query.split())
        
        return query.upper()
    
    def suggest_query_optimization(self, query):
        """Suggest optimizations for slow queries"""
        suggestions = []
        
        if 'SELECT *' in query:
            suggestions.append("Avoid SELECT *, specify only needed columns")
        
        if 'WHERE' not in query and 'SELECT' in query:
            suggestions.append("Consider adding WHERE clause to limit results")
        
        if 'ORDER BY' in query and 'LIMIT' not in query:
            suggestions.append("Consider adding LIMIT when using ORDER BY")
        
        if 'JOIN' in query:
            suggestions.append("Ensure proper indexes on JOIN columns")
        
        return suggestions
    
    def compress_response(self, data):
        """Compress response data"""
        if not self.optimizations['enable_compression']:
            return data
        
        import gzip
        
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        return gzip.compress(data)
    
    def minify_css(self, css_content):
        """Minify CSS content"""
        if not self.optimizations['enable_minification']:
            return css_content
        
        import re
        
        # Remove comments
        css_content = re.sub(r'/\*.*?\*/', '', css_content, flags=re.DOTALL)
        
        # Remove unnecessary whitespace
        css_content = re.sub(r'\s+', ' ', css_content)
        css_content = re.sub(r';\s*}', '}', css_content)
        css_content = re.sub(r'{\s*', '{', css_content)
        css_content = re.sub(r';\s*', ';', css_content)
        
        return css_content.strip()
    
    def minify_js(self, js_content):
        """Minify JavaScript content"""
        if not self.optimizations['enable_minification']:
            return js_content
        
        import re
        
        # Remove single-line comments
        js_content = re.sub(r'//.*$', '', js_content, flags=re.MULTILINE)
        
        # Remove multi-line comments
        js_content = re.sub(r'/\*.*?\*/', '', js_content, flags=re.DOTALL)
        
        # Remove unnecessary whitespace
        js_content = re.sub(r'\s+', ' ', js_content)
        js_content = re.sub(r';\s*', ';', js_content)
        js_content = re.sub(r'{\s*', '{', js_content)
        js_content = re.sub(r'}\s*', '}', js_content)
        
        return js_content.strip()
    
    def start_monitoring(self):
        """Start continuous performance monitoring"""
        def monitor_loop():
            while True:
                try:
                    # Record system metrics
                    self.record_performance_metric('cpu_usage', psutil.cpu_percent())
                    self.record_performance_metric('memory_usage', psutil.virtual_memory().percent)
                    
                    # Check for optimization opportunities
                    stats = self.get_performance_stats()
                    
                    if stats['system']['memory_percent'] > self.thresholds['memory_usage']:
                        self.optimize_memory()
                    
                    if stats['cache']['hit_rate'] < self.thresholds['cache_hit_rate']:
                        self.logger.warning(f"Low cache hit rate: {stats['cache']['hit_rate']:.1f}%")
                    
                    time.sleep(60)  # Monitor every minute
                    
                except Exception as e:
                    self.logger.error(f"Monitoring error: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor_loop)
        monitor_thread.daemon = True
        monitor_thread.start()
        
        self.logger.info("Performance monitoring started")
    
    def generate_performance_report(self):
        """Generate comprehensive performance report"""
        stats = self.get_performance_stats()
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'overall_health': self.calculate_overall_health(stats),
                'critical_issues': self.identify_critical_issues(stats),
                'optimization_suggestions': self.get_optimization_suggestions(stats)
            },
            'metrics': stats,
            'thresholds': self.thresholds,
            'optimizations_enabled': self.optimizations
        }
        
        return report
    
    def calculate_overall_health(self, stats):
        """Calculate overall system health score"""
        health_score = 100
        
        # Deduct points for issues
        if stats['system']['cpu_percent'] > self.thresholds['cpu_usage']:
            health_score -= 20
        
        if stats['system']['memory_percent'] > self.thresholds['memory_usage']:
            health_score -= 20
        
        if stats['cache']['hit_rate'] < self.thresholds['cache_hit_rate']:
            health_score -= 15
        
        if stats['response_times']['average'] > self.thresholds['response_time']:
            health_score -= 25
        
        return max(0, health_score)
    
    def identify_critical_issues(self, stats):
        """Identify critical performance issues"""
        issues = []
        
        if stats['system']['cpu_percent'] > 90:
            issues.append("Critical: CPU usage above 90%")
        
        if stats['system']['memory_percent'] > 95:
            issues.append("Critical: Memory usage above 95%")
        
        if stats['response_times']['average'] > 5:
            issues.append("Critical: Average response time above 5 seconds")
        
        return issues
    
    def get_optimization_suggestions(self, stats):
        """Get optimization suggestions based on current stats"""
        suggestions = []
        
        if stats['cache']['hit_rate'] < 70:
            suggestions.append("Enable or improve caching strategy")
        
        if stats['system']['memory_percent'] > 80:
            suggestions.append("Optimize memory usage and enable garbage collection")
        
        if stats['response_times']['average'] > 2:
            suggestions.append("Optimize database queries and enable compression")
        
        return suggestions

# Global performance optimizer instance
performance_optimizer = PerformanceOptimizer()

# Decorator for easy use
def monitor_performance(func):
    """Decorator to monitor function performance"""
    return performance_optimizer.monitor_performance(func)

if __name__ == '__main__':
    # Generate and display performance report
    report = performance_optimizer.generate_performance_report()
    print(json.dumps(report, indent=2))
