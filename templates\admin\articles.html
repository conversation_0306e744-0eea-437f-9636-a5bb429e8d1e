{% extends "admin/base.html" %}

{% block title %}إدارة المقالات{% endblock %}
{% block page_title %}إدارة المقالات{% endblock %}

{% block page_actions %}
<a href="{{ url_for('admin_new_article') }}" class="btn btn-primary-custom">
    <i class="bi bi-plus-circle me-1"></i>
    مقال جديد
</a>
{% endblock %}

{% block content %}
<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="{{ current_search }}" placeholder="ابحث في العناوين والمحتوى...">
            </div>
            <div class="col-md-4">
                <label for="status" class="form-label">الحالة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع المقالات</option>
                    <option value="published" {% if current_status == 'published' %}selected{% endif %}>منشور</option>
                    <option value="draft" {% if current_status == 'draft' %}selected{% endif %}>مسودة</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="bi bi-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Articles Table -->
<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="bi bi-journal-text me-2 text-primary-custom"></i>
            قائمة المقالات ({{ articles|length }})
        </h5>
    </div>
    <div class="card-body p-0">
        {% if articles %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="bg-light">
                    <tr>
                        <th class="px-4 py-3">العنوان</th>
                        <th class="px-4 py-3">التصنيف</th>
                        <th class="px-4 py-3">الحالة</th>
                        <th class="px-4 py-3">المشاهدات</th>
                        <th class="px-4 py-3">تاريخ الإنشاء</th>
                        <th class="px-4 py-3">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for article in articles %}
                    <tr>
                        <td class="px-4 py-3">
                            <div>
                                <h6 class="mb-1 fw-bold">{{ article.title_ar }}</h6>
                                <small class="text-muted">{{ article.excerpt[:80] }}{% if article.excerpt|length > 80 %}...{% endif %}</small>
                            </div>
                        </td>
                        <td class="px-4 py-3">
                            <span class="badge bg-primary-custom">{{ article.category }}</span>
                        </td>
                        <td class="px-4 py-3">
                            {% if article.is_published %}
                                <span class="badge bg-success">منشور</span>
                            {% else %}
                                <span class="badge bg-secondary">مسودة</span>
                            {% endif %}

                            {% if article.is_featured %}
                                <span class="badge bg-gold ms-1">مميز</span>
                            {% endif %}
                        </td>
                        <td class="px-4 py-3">
                            <span class="badge bg-info">{{ article.views or 0 }}</span>
                        </td>
                        <td class="px-4 py-3">
                            <small class="text-muted">{{ article.created_at[:10] }}</small>
                        </td>
                        <td class="px-4 py-3">
                            <div class="btn-group btn-group-sm">
                                {% if article.is_published %}
                                <a href="{{ url_for('article_detail', slug=article.slug) }}"
                                   class="btn btn-outline-info" target="_blank" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% endif %}
                                <a href="{{ url_for('admin_edit_article', article_id=article.id) }}"
                                   class="btn btn-outline-primary" title="تحرير">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <form method="POST" action="{{ url_for('admin_delete_article', article_id=article.id) }}"
                                      class="d-inline">
                                    <button type="submit" class="btn btn-outline-danger btn-delete" title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-journal-text display-1 text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد مقالات</h4>
            {% if current_search or current_status %}
            <p class="text-muted">لم يتم العثور على مقالات تطابق معايير البحث</p>
            <a href="{{ url_for('admin_articles') }}" class="btn btn-outline-primary">
                <i class="bi bi-arrow-clockwise me-1"></i>
                إزالة الفلاتر
            </a>
            {% else %}
            <p class="text-muted">ابدأ بإنشاء مقال جديد</p>
            <a href="{{ url_for('admin_new_article') }}" class="btn btn-primary-custom">
                <i class="bi bi-plus-circle me-1"></i>
                إنشاء مقال جديد
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
{% if articles %}
<div class="row mt-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-journal-text display-4 mb-2"></i>
                <h4 class="fw-bold">{{ articles|length }}</h4>
                <small>إجمالي المقالات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card success">
            <div class="card-body text-center">
                <i class="bi bi-check-circle display-4 mb-2"></i>
                <h4 class="fw-bold">
                    {% set published_count = 0 %}
                    {% for article in articles %}
                        {% if article.is_published %}
                            {% set published_count = published_count + 1 %}
                        {% endif %}
                    {% endfor %}
                    {{ published_count }}
                </h4>
                <small>منشور</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card warning">
            <div class="card-body text-center">
                <i class="bi bi-file-earmark display-4 mb-2"></i>
                <h4 class="fw-bold">
                    {% set draft_count = 0 %}
                    {% for article in articles %}
                        {% if not article.is_published %}
                            {% set draft_count = draft_count + 1 %}
                        {% endif %}
                    {% endfor %}
                    {{ draft_count }}
                </h4>
                <small>مسودة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card gold">
            <div class="card-body text-center">
                <i class="bi bi-star display-4 mb-2"></i>
                <h4 class="fw-bold">
                    {% set featured_count = 0 %}
                    {% for article in articles %}
                        {% if article.is_featured %}
                            {% set featured_count = featured_count + 1 %}
                        {% endif %}
                    {% endfor %}
                    {{ featured_count }}
                </h4>
                <small>مميز</small>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on status change
document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});
</script>
{% endblock %}
