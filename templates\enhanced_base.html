<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    <!-- SEO Meta Tags -->
    <title>{% block title %}{% endblock %} - {{ site_settings.site_name }}</title>
    <meta name="description" content="{% block meta_description %}{{ site_settings.site_description }}{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}قانون، استشارات قانونية، اجتهادات مغربية، مقالات قانونية{% endblock %}">
    <meta name="author" content="{{ site_settings.site_name }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}{{ self.title() }} - {{ site_settings.site_name }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}{{ site_settings.site_description }}{% endblock %}">
    <meta property="og:type" content="{% block og_type %}website{% endblock %}">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:site_name" content="{{ site_settings.site_name }}">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='images/branding/favicon.svg') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/branding/favicon-32x32.png') }}">
    <link rel="alternate icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/branding/favicon.svg') }}">
    <link rel="manifest" href="{{ url_for('static', filename='site.webmanifest') }}">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'gold': '#d4af37',
                        'navy-light': '#1e40af',
                        'gold-dark': '#b8941f'
                    },
                    fontFamily: {
                        'arabic': ['Noto Sans Arabic', 'sans-serif']
                    }
                }
            }
        }
    </script>

    <style>
        :root {
            --primary-navy: #1e3a8a;
            --primary-gold: #d4af37;
            --light-gray: #f8f9fa;
            --dark-gray: #6b7280;
        }

        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.6;
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-navy) !important;
        }

        .text-primary-custom { color: var(--primary-navy) !important; }
        .bg-primary-custom { background-color: var(--primary-navy) !important; }
        .text-gold { color: var(--primary-gold) !important; }
        .bg-gold { background-color: var(--primary-gold) !important; }

        .btn-primary-custom {
            background-color: var(--primary-navy);
            border-color: var(--primary-navy);
            color: white;
        }

        .btn-primary-custom:hover {
            background-color: #1e40af;
            border-color: #1e40af;
            color: white;
        }

        .btn-gold {
            background-color: var(--primary-gold);
            border-color: var(--primary-gold);
            color: white;
        }

        .btn-gold:hover {
            background-color: #b8941f;
            border-color: #b8941f;
            color: white;
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .search-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
        }

        .search-item {
            padding: 0.75rem;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .search-item:hover {
            background-color: #f8f9fa;
        }

        .search-item:last-child {
            border-bottom: none;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "‹";
            float: left;
            padding-right: 0.5rem;
            padding-left: 0;
        }

        .article-meta {
            font-size: 0.875rem;
            color: var(--dark-gray);
        }

        .view-count {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .loading-spinner {
            display: none;
        }

        .loading .loading-spinner {
            display: inline-block;
        }

        .loading .loading-text {
            display: none;
        }

        @media (max-width: 768px) {
            .display-4 { font-size: 2rem; }
            .lead { font-size: 1rem; }
            .btn-lg { padding: 0.75rem 1.5rem; font-size: 1rem; }
        }

        /* Enhanced Navigation Styles */
        .nav-link-enhanced {
            @apply flex items-center space-x-2 rtl:space-x-reverse px-4 py-2 rounded-lg text-gray-700 hover:text-navy hover:bg-gray-50 transition-all duration-300 relative overflow-hidden;
        }

        .nav-link-enhanced.active {
            @apply text-navy bg-navy/10 font-semibold;
        }

        .nav-link-enhanced.consultation-btn {
            @apply bg-gradient-to-r from-gold to-gold-dark text-white hover:from-gold-dark hover:to-gold shadow-md hover:shadow-lg;
        }

        .nav-link-enhanced.consultation-btn:hover {
            @apply text-white transform scale-105;
        }

        .nav-link-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(30, 58, 138, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-link-enhanced:hover::before {
            left: 100%;
        }

        /* Dropdown Styles */
        .dropdown-menu-enhanced {
            @apply absolute top-full right-0 mt-2 w-56 bg-white rounded-xl shadow-xl border border-gray-100 py-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-50;
        }

        .dropdown-item-enhanced {
            @apply flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 text-gray-700 hover:text-navy hover:bg-gray-50 transition-all duration-200;
        }

        /* Search Dropdown */
        .search-dropdown-enhanced {
            @apply absolute top-full left-0 right-0 mt-2 bg-white rounded-xl shadow-xl border border-gray-100 max-h-80 overflow-y-auto z-50;
        }

        .search-item-enhanced {
            @apply px-4 py-3 border-b border-gray-50 hover:bg-gray-50 cursor-pointer transition-all duration-200;
        }

        .search-item-enhanced:last-child {
            @apply border-b-0;
        }

        /* Mobile Navigation */
        .mobile-nav-item {
            @apply flex items-center space-x-3 rtl:space-x-reverse px-4 py-3 text-gray-700 hover:text-navy hover:bg-gray-50 rounded-lg transition-all duration-200;
        }

        .mobile-nav-item.active {
            @apply text-navy bg-navy/10 font-semibold;
        }

        .mobile-nav-item.consultation-mobile {
            @apply bg-gradient-to-r from-gold to-gold-dark text-white hover:from-gold-dark hover:to-gold;
        }

        /* Navbar Scroll Effect */
        .navbar-scrolled {
            @apply bg-white/98 shadow-xl;
        }

        /* Animation Classes */
        .slide-down {
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .fade-in-up {
            animation: fadeInUp 0.5s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Hover Effects */
        .hover-lift {
            transition: transform 0.3s ease;
        }

        .hover-lift:hover {
            transform: translateY(-2px);
        }

        /* Gradient Text */
        .gradient-text {
            background: linear-gradient(135deg, #1e3a8a 0%, #d4af37 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Custom Scrollbar for Search Results */
        .search-dropdown-enhanced::-webkit-scrollbar {
            width: 6px;
        }

        .search-dropdown-enhanced::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .search-dropdown-enhanced::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .search-dropdown-enhanced::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-light">
    <!-- Enhanced Navigation with Tailwind CSS -->
    <nav class="bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50 transition-all duration-300" id="navbar">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Logo Section -->
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                    <a href="{{ url_for('index') }}" class="flex items-center group">
                        <div class="relative">
                            <img src="{{ url_for('static', filename='images/branding/logo-simple.svg') }}"
                                 alt="{{ site_settings.site_name }}"
                                 class="h-12 w-auto transition-transform duration-300 group-hover:scale-110">
                            <div class="absolute inset-0 bg-gradient-to-r from-navy to-gold opacity-0 group-hover:opacity-20 rounded-full transition-opacity duration-300"></div>
                        </div>
                        <span class="hidden md:block mr-3 text-2xl font-bold bg-gradient-to-r from-navy to-gold bg-clip-text text-transparent">
                            {{ site_settings.site_name }}
                        </span>
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
                    <div class="flex items-center space-x-6 rtl:space-x-reverse">
                        <!-- Home -->
                        <a href="{{ url_for('index') }}"
                           class="nav-link-enhanced {% if request.endpoint == 'index' %}active{% endif %}">
                            <i class="bi bi-house text-lg"></i>
                            <span>الرئيسية</span>
                        </a>

                        <!-- Articles -->
                        <a href="{{ url_for('articles') }}"
                           class="nav-link-enhanced {% if request.endpoint == 'articles' %}active{% endif %}">
                            <i class="bi bi-journal-text text-lg"></i>
                            <span>المقالات</span>
                        </a>

                        <!-- Jurisprudence -->
                        <a href="{{ url_for('jurisprudence') }}"
                           class="nav-link-enhanced {% if request.endpoint == 'jurisprudence' %}active{% endif %}">
                            <i class="bi bi-bank text-lg"></i>
                            <span>الاجتهادات</span>
                        </a>

                        <!-- Consultation -->
                        <a href="{{ url_for('consultation') }}"
                           class="nav-link-enhanced consultation-btn {% if request.endpoint == 'consultation' %}active{% endif %}">
                            <i class="bi bi-chat-dots text-lg"></i>
                            <span>استشارة قانونية</span>
                        </a>

                        <!-- More Dropdown -->
                        <div class="relative group">
                            <button class="nav-link-enhanced dropdown-trigger">
                                <i class="bi bi-three-dots text-lg"></i>
                                <span>المزيد</span>
                                <i class="bi bi-chevron-down text-sm transition-transform duration-200 group-hover:rotate-180"></i>
                            </button>
                            <div class="dropdown-menu-enhanced">
                                <a href="{{ url_for('about') }}" class="dropdown-item-enhanced">
                                    <i class="bi bi-info-circle"></i>
                                    <span>من نحن</span>
                                </a>
                                <a href="{{ url_for('contact') }}" class="dropdown-item-enhanced">
                                    <i class="bi bi-envelope"></i>
                                    <span>اتصل بنا</span>
                                </a>
                                <div class="border-t border-gray-100 my-2"></div>
                                <a href="{{ url_for('privacy') }}" class="dropdown-item-enhanced">
                                    <i class="bi bi-shield-lock"></i>
                                    <span>سياسة الخصوصية</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search & Actions -->
                <div class="flex items-center space-x-4 rtl:space-x-reverse">
                    <!-- Search -->
                    <div class="relative hidden md:block">
                        <form id="searchForm" class="relative">
                            <div class="relative">
                                <input type="search"
                                       id="searchInput"
                                       placeholder="البحث في المقالات..."
                                       class="w-80 h-12 pl-12 pr-4 text-gray-700 bg-gray-50 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy transition-all duration-300 placeholder-gray-400"
                                       autocomplete="off">
                                <button type="submit"
                                        class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-navy transition-colors duration-200">
                                    <i class="bi bi-search text-lg"></i>
                                </button>
                            </div>
                        </form>
                        <div id="searchResults" class="search-dropdown-enhanced" style="display: none;"></div>
                    </div>

                    <!-- Admin Actions -->
                    {% if current_user and current_user.is_admin %}
                    <div class="flex items-center space-x-3 rtl:space-x-reverse">
                        <a href="{{ url_for('admin_dashboard') }}"
                           class="p-2 text-gray-600 hover:text-navy hover:bg-gray-100 rounded-full transition-all duration-200">
                            <i class="bi bi-speedometer2 text-lg"></i>
                        </a>
                        <a href="{{ url_for('logout') }}"
                           class="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-full transition-all duration-200">
                            <i class="bi bi-box-arrow-right text-lg"></i>
                        </a>
                    </div>
                    {% endif %}

                    <!-- Mobile Menu Button -->
                    <button class="lg:hidden p-2 text-gray-600 hover:text-navy hover:bg-gray-100 rounded-lg transition-all duration-200"
                            onclick="toggleMobileMenu()">
                        <i class="bi bi-list text-2xl" id="menuIcon"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="lg:hidden bg-white border-t border-gray-100 shadow-lg" id="mobileMenu" style="display: none;">
            <div class="px-4 py-6 space-y-4">
                <!-- Mobile Search -->
                <div class="relative mb-6">
                    <form class="relative">
                        <input type="search"
                               placeholder="البحث في المقالات..."
                               class="w-full h-12 pl-12 pr-4 text-gray-700 bg-gray-50 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy transition-all duration-300">
                        <button type="submit"
                                class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                            <i class="bi bi-search text-lg"></i>
                        </button>
                    </form>
                </div>

                <!-- Mobile Menu Items -->
                <a href="{{ url_for('index') }}" class="mobile-nav-item {% if request.endpoint == 'index' %}active{% endif %}">
                    <i class="bi bi-house text-xl"></i>
                    <span>الرئيسية</span>
                </a>

                <a href="{{ url_for('articles') }}" class="mobile-nav-item {% if request.endpoint == 'articles' %}active{% endif %}">
                    <i class="bi bi-journal-text text-xl"></i>
                    <span>المقالات</span>
                </a>

                <a href="{{ url_for('jurisprudence') }}" class="mobile-nav-item {% if request.endpoint == 'jurisprudence' %}active{% endif %}">
                    <i class="bi bi-bank text-xl"></i>
                    <span>الاجتهادات</span>
                </a>

                <a href="{{ url_for('consultation') }}" class="mobile-nav-item consultation-mobile {% if request.endpoint == 'consultation' %}active{% endif %}">
                    <i class="bi bi-chat-dots text-xl"></i>
                    <span>استشارة قانونية</span>
                </a>

                <div class="border-t border-gray-100 pt-4 mt-4">
                    <a href="{{ url_for('about') }}" class="mobile-nav-item">
                        <i class="bi bi-info-circle text-xl"></i>
                        <span>من نحن</span>
                    </a>

                    <a href="{{ url_for('contact') }}" class="mobile-nav-item">
                        <i class="bi bi-envelope text-xl"></i>
                        <span>اتصل بنا</span>
                    </a>

                    <a href="{{ url_for('privacy') }}" class="mobile-nav-item">
                        <i class="bi bi-shield-lock text-xl"></i>
                        <span>سياسة الخصوصية</span>
                    </a>
                </div>

                {% if current_user and current_user.is_admin %}
                <div class="border-t border-gray-100 pt-4 mt-4">
                    <a href="{{ url_for('admin_dashboard') }}" class="mobile-nav-item">
                        <i class="bi bi-speedometer2 text-xl"></i>
                        <span>لوحة التحكم</span>
                    </a>

                    <a href="{{ url_for('logout') }}" class="mobile-nav-item text-red-600">
                        <i class="bi bi-box-arrow-right text-xl"></i>
                        <span>تسجيل الخروج</span>
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Breadcrumb -->
    {% block breadcrumb %}{% endblock %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Back to Top Button -->
    <button id="backToTop" class="btn btn-primary-custom position-fixed"
            style="bottom: 20px; left: 20px; display: none; z-index: 1000;">
        <i class="bi bi-arrow-up"></i>
    </button>

    <!-- Footer -->
    <footer class="bg-primary-custom text-white mt-5">
        <div class="container py-5">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <img src="{{ url_for('static', filename='images/branding/logo.svg') }}"
                             alt="{{ site_settings.site_name }}"
                             height="50"
                             class="me-3">
                    </div>
                    <p class="mb-3">{{ site_settings.site_description }}</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-white fs-4"><i class="bi bi-facebook"></i></a>
                        <a href="#" class="text-white fs-4"><i class="bi bi-twitter"></i></a>
                        <a href="#" class="text-white fs-4"><i class="bi bi-linkedin"></i></a>
                        <a href="#" class="text-white fs-4"><i class="bi bi-envelope"></i></a>
                    </div>
                </div>

                <div class="col-lg-2 mb-4">
                    <h6 class="fw-bold mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('index') }}" class="text-white-50 text-decoration-none">الرئيسية</a></li>
                        <li><a href="{{ url_for('articles') }}" class="text-white-50 text-decoration-none">المقالات</a></li>
                        <li><a href="{{ url_for('jurisprudence') }}" class="text-white-50 text-decoration-none">الاجتهادات</a></li>
                        <li><a href="{{ url_for('consultation') }}" class="text-white-50 text-decoration-none">استشارة قانونية</a></li>
                        <li><a href="{{ url_for('privacy') }}" class="text-white-50 text-decoration-none">سياسة الخصوصية</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 mb-4">
                    <h6 class="fw-bold mb-3">معلومات الاتصال</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="bi bi-geo-alt me-2"></i>{{ site_settings.address }}</li>
                        <li class="mb-2"><i class="bi bi-telephone me-2"></i>{{ site_settings.contact_phone }}</li>
                        <li class="mb-2"><i class="bi bi-envelope me-2"></i>{{ site_settings.contact_email }}</li>
                    </ul>
                </div>

                <div class="col-lg-3 mb-4">
                    <h6 class="fw-bold mb-3">النشرة الإخبارية</h6>
                    <p class="small mb-3">اشترك للحصول على آخر التحديثات القانونية</p>
                    <form class="d-flex">
                        <input type="email" class="form-control me-2" placeholder="بريدك الإلكتروني">
                        <button class="btn btn-gold" type="submit">اشتراك</button>
                    </form>
                </div>
            </div>

            <hr class="my-4">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 {{ site_settings.site_name }}. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-white-50 text-decoration-none me-3">سياسة الخصوصية</a>
                    <a href="#" class="text-white-50 text-decoration-none">شروط الاستخدام</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced Navigation JavaScript

        // Mobile Menu Toggle
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            const menuIcon = document.getElementById('menuIcon');

            if (mobileMenu.style.display === 'none' || mobileMenu.style.display === '') {
                mobileMenu.style.display = 'block';
                mobileMenu.classList.add('slide-down');
                menuIcon.className = 'bi bi-x text-2xl';
            } else {
                mobileMenu.style.display = 'none';
                menuIcon.className = 'bi bi-list text-2xl';
            }
        }

        // Navbar Scroll Effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('navbar-scrolled');
            } else {
                navbar.classList.remove('navbar-scrolled');
            }
        });

        // Enhanced Search functionality
        let searchTimeout;
        const searchInput = document.getElementById('searchInput');
        const searchResults = document.getElementById('searchResults');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();

                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        fetch(`/api/search?q=${encodeURIComponent(query)}`)
                            .then(response => response.json())
                            .then(data => {
                                showSearchResults(data.results);
                            })
                            .catch(error => console.error('Search error:', error));
                    }, 300);
                } else {
                    hideSearchResults();
                }
            });

            // Search form submission
            document.getElementById('searchForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const query = searchInput.value.trim();
                if (query) {
                    window.location.href = `{{ url_for('articles') }}?search=${encodeURIComponent(query)}`;
                }
            });
        }

        function showSearchResults(results) {
            if (results.length > 0) {
                searchResults.innerHTML = results.map(result => `
                    <div class="search-item-enhanced" onclick="window.location.href='${result.url}'">
                        <div class="font-semibold text-gray-900">${result.title}</div>
                        <div class="text-gray-600 text-sm mt-1">${result.excerpt}</div>
                        <div class="text-navy text-xs mt-1 font-medium">${result.category}</div>
                    </div>
                `).join('');
                searchResults.style.display = 'block';
                searchResults.classList.add('fade-in-up');
            } else {
                searchResults.innerHTML = '<div class="search-item-enhanced text-gray-500 text-center">لا توجد نتائج</div>';
                searchResults.style.display = 'block';
            }
        }

        function hideSearchResults() {
            if (searchResults) {
                searchResults.style.display = 'none';
                searchResults.classList.remove('fade-in-up');
            }
        }

        // Hide search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.relative')) {
                hideSearchResults();
            }
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            const mobileMenu = document.getElementById('mobileMenu');
            const menuButton = e.target.closest('button');

            if (mobileMenu && mobileMenu.style.display === 'block' && !menuButton) {
                if (!e.target.closest('#mobileMenu')) {
                    toggleMobileMenu();
                }
            }
        });

        // Back to top button
        const backToTopButton = document.getElementById('backToTop');
        if (backToTopButton) {
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopButton.style.display = 'block';
                } else {
                    backToTopButton.style.display = 'none';
                }
            });

            backToTopButton.addEventListener('click', function() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        }

        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                if (typeof bootstrap !== 'undefined') {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 5000);

        // Add smooth transitions to navigation links
        document.addEventListener('DOMContentLoaded', function() {
            const navLinks = document.querySelectorAll('.nav-link-enhanced, .mobile-nav-item');

            navLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.classList.add('hover-lift');
                });

                link.addEventListener('mouseleave', function() {
                    this.classList.remove('hover-lift');
                });
            });

            // Add loading animation to consultation buttons
            const consultationBtns = document.querySelectorAll('.consultation-btn, .consultation-mobile');
            consultationBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    if (!e.ctrlKey && !e.metaKey) {
                        this.innerHTML += ' <i class="bi bi-arrow-clockwise animate-spin ml-2"></i>';
                    }
                });
            });
        });

        // Keyboard navigation support
        document.addEventListener('keydown', function(e) {
            // ESC key closes mobile menu and search results
            if (e.key === 'Escape') {
                const mobileMenu = document.getElementById('mobileMenu');
                if (mobileMenu && mobileMenu.style.display === 'block') {
                    toggleMobileMenu();
                }
                hideSearchResults();
            }

            // Ctrl/Cmd + K opens search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });

        // Add ripple effect to buttons
        function createRipple(event) {
            const button = event.currentTarget;
            const circle = document.createElement('span');
            const diameter = Math.max(button.clientWidth, button.clientHeight);
            const radius = diameter / 2;

            circle.style.width = circle.style.height = `${diameter}px`;
            circle.style.left = `${event.clientX - button.offsetLeft - radius}px`;
            circle.style.top = `${event.clientY - button.offsetTop - radius}px`;
            circle.classList.add('ripple');

            const ripple = button.getElementsByClassName('ripple')[0];
            if (ripple) {
                ripple.remove();
            }

            button.appendChild(circle);
        }

        // Apply ripple effect to buttons
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.nav-link-enhanced, .mobile-nav-item');
            buttons.forEach(button => {
                button.addEventListener('click', createRipple);
            });
        });
    </script>

    <style>
        /* Ripple Effect */
        .ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Loading Animation */
        .animate-spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
    </style>

    {% block extra_js %}{% endblock %}
</body>
</html>
