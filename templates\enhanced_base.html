<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    <!-- SEO Meta Tags -->
    <title>{% block title %}{% endblock %} - {{ site_settings.site_name }}</title>
    <meta name="description" content="{% block meta_description %}{{ site_settings.site_description }}{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}قانون، استشارات قانونية، اجتهادات مغربية، مقالات قانونية{% endblock %}">
    <meta name="author" content="{{ site_settings.site_name }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}{{ self.title() }} - {{ site_settings.site_name }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}{{ site_settings.site_description }}{% endblock %}">
    <meta property="og:type" content="{% block og_type %}website{% endblock %}">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:site_name" content="{{ site_settings.site_name }}">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='images/branding/favicon.svg') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/branding/favicon-32x32.png') }}">
    <link rel="alternate icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/branding/favicon.svg') }}">
    <link rel="manifest" href="{{ url_for('static', filename='site.webmanifest') }}">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Beautiful Arabic Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&family=Cairo:wght@300;400;500;600;700;800&family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'royal-blue': '#1e40af',
                        'deep-navy': '#0f172a',
                        'golden': '#f59e0b',
                        'amber': '#fbbf24',
                        'emerald': '#10b981',
                        'rose': '#f43f5e',
                        'purple': '#8b5cf6',
                        'teal': '#14b8a6'
                    },
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                        'amiri': ['Amiri', 'serif'],
                        'tajawal': ['Tajawal', 'sans-serif']
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'glow': 'glow 2s ease-in-out infinite alternate',
                        'slide-down': 'slideDown 0.3s ease-out',
                        'fade-in': 'fadeIn 0.5s ease-out'
                    }
                }
            }
        }
    </script>

    <style>
        :root {
            --primary-navy: #1e3a8a;
            --primary-gold: #d4af37;
            --light-gray: #f8f9fa;
            --dark-gray: #6b7280;
        }

        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.6;
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-navy) !important;
        }

        .text-primary-custom { color: var(--primary-navy) !important; }
        .bg-primary-custom { background-color: var(--primary-navy) !important; }
        .text-gold { color: var(--primary-gold) !important; }
        .bg-gold { background-color: var(--primary-gold) !important; }

        .btn-primary-custom {
            background-color: var(--primary-navy);
            border-color: var(--primary-navy);
            color: white;
        }

        .btn-primary-custom:hover {
            background-color: #1e40af;
            border-color: #1e40af;
            color: white;
        }

        .btn-gold {
            background-color: var(--primary-gold);
            border-color: var(--primary-gold);
            color: white;
        }

        .btn-gold:hover {
            background-color: #b8941f;
            border-color: #b8941f;
            color: white;
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .search-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
        }

        .search-item {
            padding: 0.75rem;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .search-item:hover {
            background-color: #f8f9fa;
        }

        .search-item:last-child {
            border-bottom: none;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "‹";
            float: left;
            padding-right: 0.5rem;
            padding-left: 0;
        }

        .article-meta {
            font-size: 0.875rem;
            color: var(--dark-gray);
        }

        .view-count {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .loading-spinner {
            display: none;
        }

        .loading .loading-spinner {
            display: inline-block;
        }

        .loading .loading-text {
            display: none;
        }

        @media (max-width: 768px) {
            .display-4 { font-size: 2rem; }
            .lead { font-size: 1rem; }
            .btn-lg { padding: 0.75rem 1.5rem; font-size: 1rem; }
        }

        /* Beautiful Navigation Styles */

        /* Top Bar */
        .top-bar {
            background: linear-gradient(135deg, #0f172a 0%, #1e40af 100%);
            border-bottom: 1px solid rgba(251, 191, 36, 0.2);
        }

        .social-links {
            display: flex;
            gap: 0.5rem;
        }

        .social-link {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background: rgba(251, 191, 36, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fbbf24;
            transition: all 0.3s ease;
            border: 1px solid rgba(251, 191, 36, 0.2);
        }

        .social-link:hover {
            background: #fbbf24;
            color: #0f172a;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(251, 191, 36, 0.3);
        }

        .admin-link {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.75rem;
            background: rgba(251, 191, 36, 0.1);
            border: 1px solid rgba(251, 191, 36, 0.2);
            border-radius: 20px;
            color: #fbbf24;
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .admin-link:hover {
            background: #fbbf24;
            color: #0f172a;
        }

        /* Main Navigation */
        .beautiful-navbar {
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .main-nav {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-bottom: 3px solid transparent;
            border-image: linear-gradient(90deg, #1e40af, #fbbf24, #10b981) 1;
        }

        /* Logo Section */
        .logo-section {
            position: relative;
        }

        .logo-link {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .logo-container {
            position: relative;
            width: 60px;
            height: 60px;
        }

        .logo-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: all 0.3s ease;
            filter: drop-shadow(0 4px 8px rgba(30, 64, 175, 0.2));
        }

        .logo-glow {
            position: absolute;
            inset: -10px;
            background: radial-gradient(circle, rgba(251, 191, 36, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
            animation: glow 3s ease-in-out infinite alternate;
        }

        .logo-link:hover .logo-image {
            transform: scale(1.1) rotate(5deg);
        }

        .logo-link:hover .logo-glow {
            opacity: 1;
        }

        .logo-text {
            display: flex;
            flex-direction: column;
        }

        .site-name {
            font-size: 1.75rem;
            font-weight: 700;
            background: linear-gradient(135deg, #1e40af 0%, #fbbf24 50%, #10b981 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
            line-height: 1.2;
        }

        .site-tagline {
            font-size: 0.875rem;
            color: #64748b;
            margin: 0;
            font-weight: 500;
        }

        /* Navigation Menu */
        .nav-menu {
            flex: 1;
            justify-content: center;
        }

        .nav-items {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            padding: 0.5rem;
            border-radius: 50px;
            border: 1px solid rgba(30, 64, 175, 0.1);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .nav-item {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 0.75rem 1.25rem;
            border-radius: 25px;
            text-decoration: none;
            transition: all 0.3s ease;
            overflow: hidden;
            min-width: 120px;
        }

        .nav-icon {
            font-size: 1.25rem;
            margin-bottom: 0.25rem;
            transition: all 0.3s ease;
        }

        .nav-text {
            font-size: 0.875rem;
            font-weight: 600;
            text-align: center;
            line-height: 1.2;
        }

        .nav-indicator {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 3px;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        /* Navigation Item Colors */
        .home-item {
            color: #1e40af;
        }
        .home-item:hover {
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.1) 0%, rgba(30, 64, 175, 0.05) 100%);
            color: #1e40af;
            transform: translateY(-2px);
        }
        .home-item.active .nav-indicator,
        .home-item:hover .nav-indicator {
            width: 80%;
            background: #1e40af;
        }

        .articles-item {
            color: #10b981;
        }
        .articles-item:hover {
            background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
            color: #10b981;
            transform: translateY(-2px);
        }
        .articles-item.active .nav-indicator,
        .articles-item:hover .nav-indicator {
            width: 80%;
            background: #10b981;
        }

        .jurisprudence-item {
            color: #8b5cf6;
        }
        .jurisprudence-item:hover {
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(139, 92, 246, 0.05) 100%);
            color: #8b5cf6;
            transform: translateY(-2px);
        }
        .jurisprudence-item.active .nav-indicator,
        .jurisprudence-item:hover .nav-indicator {
            width: 80%;
            background: #8b5cf6;
        }

        .consultation-item {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
            position: relative;
            box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
        }
        .consultation-item:hover {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(251, 191, 36, 0.4);
        }

        .consultation-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ef4444;
            color: white;
            font-size: 0.625rem;
            padding: 0.125rem 0.375rem;
            border-radius: 10px;
            font-weight: 700;
            animation: pulse 2s infinite;
        }

        /* Dropdown */
        .nav-dropdown {
            position: relative;
        }

        .dropdown-trigger {
            color: #64748b;
            cursor: pointer;
        }
        .dropdown-trigger:hover {
            background: linear-gradient(135deg, rgba(100, 116, 139, 0.1) 0%, rgba(100, 116, 139, 0.05) 100%);
            color: #64748b;
            transform: translateY(-2px);
        }

        .dropdown-arrow {
            margin-right: 0.5rem;
            transition: transform 0.3s ease;
        }

        .nav-dropdown:hover .dropdown-arrow {
            transform: rotate(180deg);
        }

        .dropdown-menu {
            position: absolute;
            top: calc(100% + 1rem);
            right: 0;
            width: 320px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(30, 64, 175, 0.1);
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .nav-dropdown:hover .dropdown-menu {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 1.5rem 1.5rem 1rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .dropdown-items {
            padding: 0.5rem;
        }

        .dropdown-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 15px;
            text-decoration: none;
            transition: all 0.3s ease;
            margin-bottom: 0.25rem;
        }

        .dropdown-item:hover {
            background: #f8fafc;
            transform: translateX(-5px);
        }

        .dropdown-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .about-icon {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .contact-icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .privacy-icon {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
        }

        .terms-icon {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }

        .dropdown-content {
            display: flex;
            flex-direction: column;
        }

        .dropdown-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.25rem;
        }

        .dropdown-desc {
            font-size: 0.875rem;
            color: #64748b;
        }

        /* Search Styles */
        .search-container {
            position: relative;
        }

        .search-form {
            position: relative;
        }

        .search-input-wrapper {
            position: relative;
            width: 350px;
        }

        .search-input {
            width: 100%;
            height: 48px;
            padding: 0 3rem 0 1.5rem;
            border: 2px solid transparent;
            border-radius: 25px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            font-size: 0.875rem;
            transition: all 0.3s ease;
            outline: none;
        }

        .search-input:focus {
            border-color: #1e40af;
            background: white;
            box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.1);
        }

        .search-input::placeholder {
            color: #94a3b8;
        }

        .search-button {
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .search-button:hover {
            background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
            transform: translateY(-50%) scale(1.1);
        }

        .search-decoration {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            border-radius: 50%;
            opacity: 0.3;
        }

        .search-results {
            position: absolute;
            top: calc(100% + 0.5rem);
            left: 0;
            right: 0;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(30, 64, 175, 0.1);
            max-height: 400px;
            overflow-y: auto;
            z-index: 1000;
        }

        .search-result-item {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .search-result-item:hover {
            background: #f8fafc;
        }

        .search-result-item:last-child {
            border-bottom: none;
        }

        /* Mobile Menu Button */
        .mobile-menu-btn {
            width: 44px;
            height: 44px;
            border: none;
            border-radius: 12px;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .mobile-menu-btn:hover {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
        }

        .menu-line {
            width: 20px;
            height: 2px;
            background: #64748b;
            border-radius: 1px;
            transition: all 0.3s ease;
        }

        .mobile-menu-btn:hover .menu-line {
            background: white;
        }

        .mobile-menu-btn.active .menu-line:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }

        .mobile-menu-btn.active .menu-line:nth-child(2) {
            opacity: 0;
        }

        .mobile-menu-btn.active .menu-line:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }

        /* Mobile Navigation */
        .mobile-nav {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e2e8f0;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            z-index: 999;
        }

        .mobile-nav-content {
            padding: 1.5rem;
        }

        .mobile-search {
            margin-bottom: 2rem;
        }

        .mobile-search-form {
            position: relative;
        }

        .mobile-search-input {
            width: 100%;
            height: 48px;
            padding: 0 3rem 0 1.5rem;
            border: 2px solid #e2e8f0;
            border-radius: 25px;
            background: #f8fafc;
            font-size: 0.875rem;
            transition: all 0.3s ease;
            outline: none;
        }

        .mobile-search-input:focus {
            border-color: #1e40af;
            background: white;
        }

        .mobile-search-btn {
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mobile-menu-items {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .mobile-nav-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 15px;
            text-decoration: none;
            transition: all 0.3s ease;
            position: relative;
        }

        .mobile-nav-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            transition: all 0.3s ease;
        }

        .mobile-nav-text {
            flex: 1;
            font-weight: 600;
            color: #1e293b;
        }

        .mobile-nav-arrow {
            color: #94a3b8;
            transition: all 0.3s ease;
        }

        .mobile-nav-item:hover .mobile-nav-arrow {
            color: #1e40af;
            transform: translateX(-3px);
        }

        /* Mobile Navigation Item Colors */
        .mobile-nav-item.home .mobile-nav-icon {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
        }
        .mobile-nav-item.home:hover {
            background: rgba(30, 64, 175, 0.05);
        }

        .mobile-nav-item.articles .mobile-nav-icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        .mobile-nav-item.articles:hover {
            background: rgba(16, 185, 129, 0.05);
        }

        .mobile-nav-item.jurisprudence .mobile-nav-icon {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
        }
        .mobile-nav-item.jurisprudence:hover {
            background: rgba(139, 92, 246, 0.05);
        }

        .mobile-nav-item.consultation {
            background: linear-gradient(135deg, rgba(251, 191, 36, 0.1) 0%, rgba(245, 158, 11, 0.1) 100%);
            border: 1px solid rgba(251, 191, 36, 0.2);
        }
        .mobile-nav-item.consultation .mobile-nav-icon {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
        }

        .mobile-nav-item.about .mobile-nav-icon {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }
        .mobile-nav-item.about:hover {
            background: rgba(59, 130, 246, 0.05);
        }

        .mobile-nav-item.contact .mobile-nav-icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }
        .mobile-nav-item.contact:hover {
            background: rgba(16, 185, 129, 0.05);
        }

        .mobile-nav-item.privacy .mobile-nav-icon {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            color: white;
        }
        .mobile-nav-item.privacy:hover {
            background: rgba(139, 92, 246, 0.05);
        }

        .mobile-nav-item.terms .mobile-nav-icon {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
        }
        .mobile-nav-item.terms:hover {
            background: rgba(245, 158, 11, 0.05);
        }

        .mobile-nav-badge {
            background: #ef4444;
            color: white;
            font-size: 0.625rem;
            padding: 0.125rem 0.375rem;
            border-radius: 10px;
            font-weight: 700;
        }

        .mobile-nav-divider {
            height: 1px;
            background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
            margin: 1rem 0;
        }

        .mobile-contact {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e2e8f0;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .mobile-contact-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: #64748b;
            font-size: 0.875rem;
        }

        /* Animations */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @keyframes glow {
            0% { opacity: 0.3; }
            100% { opacity: 0.8; }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .search-input-wrapper {
                width: 280px;
            }
        }

        @media (max-width: 768px) {
            .site-name {
                font-size: 1.5rem;
            }

            .logo-container {
                width: 50px;
                height: 50px;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-light">
    <!-- Beautiful Enhanced Navigation -->
    <nav class="beautiful-navbar" id="navbar">
        <!-- Top Bar with Contact Info -->
        <div class="top-bar">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center h-10 text-sm">
                    <div class="flex items-center space-x-6 rtl:space-x-reverse text-white/80">
                        <div class="flex items-center space-x-2 rtl:space-x-reverse">
                            <i class="bi bi-telephone text-golden"></i>
                            <span class="font-tajawal">{{ site_settings.contact_phone }}</span>
                        </div>
                        <div class="flex items-center space-x-2 rtl:space-x-reverse">
                            <i class="bi bi-envelope text-golden"></i>
                            <span class="font-tajawal">{{ site_settings.contact_email }}</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 rtl:space-x-reverse">
                        <div class="social-links">
                            <a href="#" class="social-link"><i class="bi bi-facebook"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-twitter"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-linkedin"></i></a>
                            <a href="#" class="social-link"><i class="bi bi-instagram"></i></a>
                        </div>
                        {% if current_user and current_user.is_admin %}
                        <div class="admin-quick-links">
                            <a href="{{ url_for('admin_dashboard') }}" class="admin-link">
                                <i class="bi bi-speedometer2"></i>
                                <span>لوحة التحكم</span>
                            </a>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Navigation -->
        <div class="main-nav">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center h-20">
                    <!-- Logo Section -->
                    <div class="logo-section">
                        <a href="{{ url_for('index') }}" class="logo-link">
                            <div class="logo-container">
                                <img src="{{ url_for('static', filename='images/branding/logo-simple.svg') }}"
                                     alt="{{ site_settings.site_name }}"
                                     class="logo-image">
                                <div class="logo-glow"></div>
                            </div>
                            <div class="logo-text">
                                <h1 class="site-name font-amiri">{{ site_settings.site_name }}</h1>
                                <p class="site-tagline font-cairo">موقع قانوني متخصص</p>
                            </div>
                        </a>
                    </div>

                    <!-- Desktop Navigation Menu -->
                    <div class="hidden lg:flex nav-menu">
                        <div class="nav-items">
                            <!-- Home -->
                            <a href="{{ url_for('index') }}"
                               class="nav-item home-item {% if request.endpoint == 'index' %}active{% endif %}">
                                <div class="nav-icon">
                                    <i class="bi bi-house-heart"></i>
                                </div>
                                <span class="nav-text font-cairo">الرئيسية</span>
                                <div class="nav-indicator"></div>
                            </a>

                            <!-- Articles -->
                            <a href="{{ url_for('articles') }}"
                               class="nav-item articles-item {% if request.endpoint == 'articles' %}active{% endif %}">
                                <div class="nav-icon">
                                    <i class="bi bi-journal-bookmark"></i>
                                </div>
                                <span class="nav-text font-cairo">المقالات القانونية</span>
                                <div class="nav-indicator"></div>
                            </a>

                            <!-- Jurisprudence -->
                            <a href="{{ url_for('jurisprudence') }}"
                               class="nav-item jurisprudence-item {% if request.endpoint == 'jurisprudence' %}active{% endif %}">
                                <div class="nav-icon">
                                    <i class="bi bi-bank2"></i>
                                </div>
                                <span class="nav-text font-cairo">الاجتهادات المغربية</span>
                                <div class="nav-indicator"></div>
                            </a>

                            <!-- Consultation -->
                            <a href="{{ url_for('consultation') }}"
                               class="nav-item consultation-item {% if request.endpoint == 'consultation' %}active{% endif %}">
                                <div class="nav-icon">
                                    <i class="bi bi-chat-heart"></i>
                                </div>
                                <span class="nav-text font-cairo">استشارة قانونية</span>
                                <div class="nav-indicator"></div>
                                <div class="consultation-badge">مجاني</div>
                            </a>

                            <!-- More Dropdown -->
                            <div class="nav-dropdown">
                                <button class="nav-item dropdown-trigger">
                                    <div class="nav-icon">
                                        <i class="bi bi-grid-3x3-gap"></i>
                                    </div>
                                    <span class="nav-text font-cairo">المزيد</span>
                                    <i class="bi bi-chevron-down dropdown-arrow"></i>
                                </button>
                                <div class="dropdown-menu">
                                    <div class="dropdown-header">
                                        <h4 class="font-cairo font-bold text-deep-navy">صفحات إضافية</h4>
                                        <p class="text-gray-600 text-sm">معلومات مهمة عن الموقع</p>
                                    </div>
                                    <div class="dropdown-items">
                                        <a href="{{ url_for('about') }}" class="dropdown-item">
                                            <div class="dropdown-icon about-icon">
                                                <i class="bi bi-info-circle-fill"></i>
                                            </div>
                                            <div class="dropdown-content">
                                                <span class="dropdown-title font-cairo">من نحن</span>
                                                <span class="dropdown-desc">تعرف على فريقنا</span>
                                            </div>
                                        </a>
                                        <a href="{{ url_for('contact') }}" class="dropdown-item">
                                            <div class="dropdown-icon contact-icon">
                                                <i class="bi bi-envelope-heart"></i>
                                            </div>
                                            <div class="dropdown-content">
                                                <span class="dropdown-title font-cairo">اتصل بنا</span>
                                                <span class="dropdown-desc">تواصل معنا مباشرة</span>
                                            </div>
                                        </a>
                                        <a href="{{ url_for('privacy') }}" class="dropdown-item">
                                            <div class="dropdown-icon privacy-icon">
                                                <i class="bi bi-shield-lock-fill"></i>
                                            </div>
                                            <div class="dropdown-content">
                                                <span class="dropdown-title font-cairo">سياسة الخصوصية</span>
                                                <span class="dropdown-desc">حماية بياناتك</span>
                                            </div>
                                        </a>
                                        <a href="{{ url_for('terms') }}" class="dropdown-item">
                                            <div class="dropdown-icon terms-icon">
                                                <i class="bi bi-file-text-fill"></i>
                                            </div>
                                            <div class="dropdown-content">
                                                <span class="dropdown-title font-cairo">شروط الاستخدام</span>
                                                <span class="dropdown-desc">الأحكام والشروط</span>
                                            </div>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Search & Actions -->
                    <div class="nav-actions">
                        <!-- Beautiful Search -->
                        <div class="search-container hidden md:block">
                            <form id="searchForm" class="search-form">
                                <div class="search-input-wrapper">
                                    <input type="search"
                                           id="searchInput"
                                           placeholder="ابحث في المقالات والاجتهادات..."
                                           class="search-input font-cairo"
                                           autocomplete="off">
                                    <button type="submit" class="search-button">
                                        <i class="bi bi-search"></i>
                                    </button>
                                    <div class="search-decoration"></div>
                                </div>
                            </form>
                            <div id="searchResults" class="search-results" style="display: none;"></div>
                        </div>

                        <!-- Mobile Menu Button -->
                        <button class="mobile-menu-btn lg:hidden" onclick="toggleMobileMenu()">
                            <span class="menu-line"></span>
                            <span class="menu-line"></span>
                            <span class="menu-line"></span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="mobile-nav" id="mobileMenu" style="display: none;">
            <div class="mobile-nav-content">
                <!-- Mobile Search -->
                <div class="mobile-search">
                    <form class="mobile-search-form">
                        <input type="search"
                               placeholder="البحث في الموقع..."
                               class="mobile-search-input font-cairo">
                        <button type="submit" class="mobile-search-btn">
                            <i class="bi bi-search"></i>
                        </button>
                    </form>
                </div>

                <!-- Mobile Menu Items -->
                <div class="mobile-menu-items">
                    <a href="{{ url_for('index') }}" class="mobile-nav-item home {% if request.endpoint == 'index' %}active{% endif %}">
                        <div class="mobile-nav-icon">
                            <i class="bi bi-house-heart"></i>
                        </div>
                        <span class="mobile-nav-text font-cairo">الرئيسية</span>
                        <i class="bi bi-chevron-left mobile-nav-arrow"></i>
                    </a>

                    <a href="{{ url_for('articles') }}" class="mobile-nav-item articles {% if request.endpoint == 'articles' %}active{% endif %}">
                        <div class="mobile-nav-icon">
                            <i class="bi bi-journal-bookmark"></i>
                        </div>
                        <span class="mobile-nav-text font-cairo">المقالات القانونية</span>
                        <i class="bi bi-chevron-left mobile-nav-arrow"></i>
                    </a>

                    <a href="{{ url_for('jurisprudence') }}" class="mobile-nav-item jurisprudence {% if request.endpoint == 'jurisprudence' %}active{% endif %}">
                        <div class="mobile-nav-icon">
                            <i class="bi bi-bank2"></i>
                        </div>
                        <span class="mobile-nav-text font-cairo">الاجتهادات المغربية</span>
                        <i class="bi bi-chevron-left mobile-nav-arrow"></i>
                    </a>

                    <a href="{{ url_for('consultation') }}" class="mobile-nav-item consultation {% if request.endpoint == 'consultation' %}active{% endif %}">
                        <div class="mobile-nav-icon">
                            <i class="bi bi-chat-heart"></i>
                        </div>
                        <div class="mobile-nav-content">
                            <span class="mobile-nav-text font-cairo">استشارة قانونية</span>
                            <span class="mobile-nav-badge">مجاني</span>
                        </div>
                        <i class="bi bi-chevron-left mobile-nav-arrow"></i>
                    </a>

                    <div class="mobile-nav-divider"></div>

                    <a href="{{ url_for('about') }}" class="mobile-nav-item about">
                        <div class="mobile-nav-icon">
                            <i class="bi bi-info-circle-fill"></i>
                        </div>
                        <span class="mobile-nav-text font-cairo">من نحن</span>
                        <i class="bi bi-chevron-left mobile-nav-arrow"></i>
                    </a>

                    <a href="{{ url_for('contact') }}" class="mobile-nav-item contact">
                        <div class="mobile-nav-icon">
                            <i class="bi bi-envelope-heart"></i>
                        </div>
                        <span class="mobile-nav-text font-cairo">اتصل بنا</span>
                        <i class="bi bi-chevron-left mobile-nav-arrow"></i>
                    </a>

                    <a href="{{ url_for('privacy') }}" class="mobile-nav-item privacy">
                        <div class="mobile-nav-icon">
                            <i class="bi bi-shield-lock-fill"></i>
                        </div>
                        <span class="mobile-nav-text font-cairo">سياسة الخصوصية</span>
                        <i class="bi bi-chevron-left mobile-nav-arrow"></i>
                    </a>

                    <a href="{{ url_for('terms') }}" class="mobile-nav-item terms">
                        <div class="mobile-nav-icon">
                            <i class="bi bi-file-text-fill"></i>
                        </div>
                        <span class="mobile-nav-text font-cairo">شروط الاستخدام</span>
                        <i class="bi bi-chevron-left mobile-nav-arrow"></i>
                    </a>

                    {% if current_user and current_user.is_admin %}
                    <div class="mobile-nav-divider"></div>
                    <a href="{{ url_for('admin_dashboard') }}" class="mobile-nav-item admin">
                        <div class="mobile-nav-icon">
                            <i class="bi bi-speedometer2"></i>
                        </div>
                        <span class="mobile-nav-text font-cairo">لوحة التحكم</span>
                        <i class="bi bi-chevron-left mobile-nav-arrow"></i>
                    </a>

                    <a href="{{ url_for('logout') }}" class="mobile-nav-item logout">
                        <div class="mobile-nav-icon">
                            <i class="bi bi-box-arrow-right"></i>
                        </div>
                        <span class="mobile-nav-text font-cairo">تسجيل الخروج</span>
                        <i class="bi bi-chevron-left mobile-nav-arrow"></i>
                    </a>
                    {% endif %}
                </div>

                <!-- Mobile Contact Info -->
                <div class="mobile-contact">
                    <div class="mobile-contact-item">
                        <i class="bi bi-telephone text-golden"></i>
                        <span class="font-tajawal">{{ site_settings.contact_phone }}</span>
                    </div>
                    <div class="mobile-contact-item">
                        <i class="bi bi-envelope text-golden"></i>
                        <span class="font-tajawal">{{ site_settings.contact_email }}</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Breadcrumb -->
    {% block breadcrumb %}{% endblock %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Back to Top Button -->
    <button id="backToTop" class="btn btn-primary-custom position-fixed"
            style="bottom: 20px; left: 20px; display: none; z-index: 1000;">
        <i class="bi bi-arrow-up"></i>
    </button>

    <!-- Footer -->
    <footer class="bg-primary-custom text-white mt-5">
        <div class="container py-5">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <img src="{{ url_for('static', filename='images/branding/logo.svg') }}"
                             alt="{{ site_settings.site_name }}"
                             height="50"
                             class="me-3">
                    </div>
                    <p class="mb-3">{{ site_settings.site_description }}</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-white fs-4"><i class="bi bi-facebook"></i></a>
                        <a href="#" class="text-white fs-4"><i class="bi bi-twitter"></i></a>
                        <a href="#" class="text-white fs-4"><i class="bi bi-linkedin"></i></a>
                        <a href="#" class="text-white fs-4"><i class="bi bi-envelope"></i></a>
                    </div>
                </div>

                <div class="col-lg-2 mb-4">
                    <h6 class="fw-bold mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('index') }}" class="text-white-50 text-decoration-none">الرئيسية</a></li>
                        <li><a href="{{ url_for('articles') }}" class="text-white-50 text-decoration-none">المقالات</a></li>
                        <li><a href="{{ url_for('jurisprudence') }}" class="text-white-50 text-decoration-none">الاجتهادات</a></li>
                        <li><a href="{{ url_for('consultation') }}" class="text-white-50 text-decoration-none">استشارة قانونية</a></li>
                        <li><a href="{{ url_for('privacy') }}" class="text-white-50 text-decoration-none">سياسة الخصوصية</a></li>
                        <li><a href="{{ url_for('terms') }}" class="text-white-50 text-decoration-none">شروط الاستخدام</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 mb-4">
                    <h6 class="fw-bold mb-3">معلومات الاتصال</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="bi bi-geo-alt me-2"></i>{{ site_settings.address }}</li>
                        <li class="mb-2"><i class="bi bi-telephone me-2"></i>{{ site_settings.contact_phone }}</li>
                        <li class="mb-2"><i class="bi bi-envelope me-2"></i>{{ site_settings.contact_email }}</li>
                    </ul>
                </div>

                <div class="col-lg-3 mb-4">
                    <h6 class="fw-bold mb-3">النشرة الإخبارية</h6>
                    <p class="small mb-3">اشترك للحصول على آخر التحديثات القانونية</p>
                    <form class="d-flex">
                        <input type="email" class="form-control me-2" placeholder="بريدك الإلكتروني">
                        <button class="btn btn-gold" type="submit">اشتراك</button>
                    </form>
                </div>
            </div>

            <hr class="my-4">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 {{ site_settings.site_name }}. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="{{ url_for('privacy') }}" class="text-white-50 text-decoration-none me-3">سياسة الخصوصية</a>
                    <a href="{{ url_for('terms') }}" class="text-white-50 text-decoration-none">شروط الاستخدام</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Beautiful Navigation JavaScript

        // Mobile Menu Toggle
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            const menuBtn = document.querySelector('.mobile-menu-btn');

            if (mobileMenu.style.display === 'none' || mobileMenu.style.display === '') {
                mobileMenu.style.display = 'block';
                mobileMenu.style.animation = 'slideDown 0.3s ease-out';
                menuBtn.classList.add('active');
            } else {
                mobileMenu.style.display = 'none';
                menuBtn.classList.remove('active');
            }
        }

        // Navbar Scroll Effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('navbar-scrolled');
            } else {
                navbar.classList.remove('navbar-scrolled');
            }
        });

        // Enhanced Search functionality
        let searchTimeout;
        const searchInput = document.getElementById('searchInput');
        const searchResults = document.getElementById('searchResults');

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const query = this.value.trim();

                if (query.length >= 2) {
                    searchTimeout = setTimeout(() => {
                        fetch(`/api/search?q=${encodeURIComponent(query)}`)
                            .then(response => response.json())
                            .then(data => {
                                showSearchResults(data.results);
                            })
                            .catch(error => console.error('Search error:', error));
                    }, 300);
                } else {
                    hideSearchResults();
                }
            });

            // Search form submission
            document.getElementById('searchForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const query = searchInput.value.trim();
                if (query) {
                    window.location.href = `{{ url_for('articles') }}?search=${encodeURIComponent(query)}`;
                }
            });
        }

        function showSearchResults(results) {
            if (results.length > 0) {
                searchResults.innerHTML = results.map(result => `
                    <div class="search-result-item" onclick="window.location.href='${result.url}'">
                        <div class="font-semibold text-gray-900 font-cairo">${result.title}</div>
                        <div class="text-gray-600 text-sm mt-1 font-tajawal">${result.excerpt}</div>
                        <div class="text-royal-blue text-xs mt-1 font-medium font-cairo">${result.category}</div>
                    </div>
                `).join('');
                searchResults.style.display = 'block';
                searchResults.style.animation = 'fadeIn 0.3s ease-out';
            } else {
                searchResults.innerHTML = '<div class="search-result-item text-gray-500 text-center font-cairo">لا توجد نتائج</div>';
                searchResults.style.display = 'block';
            }
        }

        function hideSearchResults() {
            if (searchResults) {
                searchResults.style.display = 'none';
                searchResults.classList.remove('fade-in-up');
            }
        }

        // Hide search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.relative')) {
                hideSearchResults();
            }
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            const mobileMenu = document.getElementById('mobileMenu');
            const menuButton = e.target.closest('button');

            if (mobileMenu && mobileMenu.style.display === 'block' && !menuButton) {
                if (!e.target.closest('#mobileMenu')) {
                    toggleMobileMenu();
                }
            }
        });

        // Back to top button
        const backToTopButton = document.getElementById('backToTop');
        if (backToTopButton) {
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopButton.style.display = 'block';
                } else {
                    backToTopButton.style.display = 'none';
                }
            });

            backToTopButton.addEventListener('click', function() {
                window.scrollTo({ top: 0, behavior: 'smooth' });
            });
        }

        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                if (typeof bootstrap !== 'undefined') {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            });
        }, 5000);

        // Beautiful Navigation Effects
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to navigation items
            const navItems = document.querySelectorAll('.nav-item, .mobile-nav-item');

            navItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // Add click animation to consultation buttons
            const consultationBtns = document.querySelectorAll('.consultation-item, .mobile-nav-item.consultation');
            consultationBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    if (!e.ctrlKey && !e.metaKey) {
                        // Add loading effect
                        const icon = this.querySelector('.nav-icon i, .mobile-nav-icon i');
                        if (icon) {
                            icon.style.animation = 'spin 1s linear infinite';
                            setTimeout(() => {
                                icon.style.animation = '';
                            }, 2000);
                        }
                    }
                });
            });

            // Add parallax effect to logo
            const logo = document.querySelector('.logo-image');
            if (logo) {
                window.addEventListener('scroll', () => {
                    const scrolled = window.pageYOffset;
                    const rate = scrolled * -0.5;
                    logo.style.transform = `translateY(${rate}px)`;
                });
            }

            // Add floating animation to navigation items
            const floatingItems = document.querySelectorAll('.nav-item');
            floatingItems.forEach((item, index) => {
                item.style.animationDelay = `${index * 0.1}s`;
                item.classList.add('animate-float');
            });

            // Add glow effect to active navigation item
            const activeItem = document.querySelector('.nav-item.active');
            if (activeItem) {
                activeItem.style.boxShadow = '0 0 20px rgba(30, 64, 175, 0.3)';
            }
        });

        // Keyboard navigation support
        document.addEventListener('keydown', function(e) {
            // ESC key closes mobile menu and search results
            if (e.key === 'Escape') {
                const mobileMenu = document.getElementById('mobileMenu');
                if (mobileMenu && mobileMenu.style.display === 'block') {
                    toggleMobileMenu();
                }
                hideSearchResults();
            }

            // Ctrl/Cmd + K opens search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });

        // Add ripple effect to buttons
        function createRipple(event) {
            const button = event.currentTarget;
            const circle = document.createElement('span');
            const diameter = Math.max(button.clientWidth, button.clientHeight);
            const radius = diameter / 2;

            circle.style.width = circle.style.height = `${diameter}px`;
            circle.style.left = `${event.clientX - button.offsetLeft - radius}px`;
            circle.style.top = `${event.clientY - button.offsetTop - radius}px`;
            circle.classList.add('ripple');

            const ripple = button.getElementsByClassName('ripple')[0];
            if (ripple) {
                ripple.remove();
            }

            button.appendChild(circle);
        }

        // Apply ripple effect to buttons
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.nav-link-enhanced, .mobile-nav-item');
            buttons.forEach(button => {
                button.addEventListener('click', createRipple);
            });
        });
    </script>

    <style>
        /* Ripple Effect */
        .ripple {
            position: absolute;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Loading Animation */
        .animate-spin {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
    </style>

    {% block extra_js %}{% endblock %}
</body>
</html>
