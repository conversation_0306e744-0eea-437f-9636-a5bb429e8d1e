<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    <!-- SEO Meta Tags -->
    <title>{% block title %}{% endblock %} - {{ site_settings.site_name }}</title>
    <meta name="description" content="{% block meta_description %}{{ site_settings.site_description }}{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}قانون، استشارات قانونية، اجتهادات مغربية، مقالات قانونية{% endblock %}">
    <meta name="author" content="{{ site_settings.site_name }}">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}{{ self.title() }} - {{ site_settings.site_name }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}{{ site_settings.site_description }}{% endblock %}">
    <meta property="og:type" content="{% block og_type %}website{% endblock %}">
    <meta property="og:url" content="{{ request.url }}">
    <meta property="og:site_name" content="{{ site_settings.site_name }}">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='images/branding/favicon.svg') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='images/branding/favicon-32x32.png') }}">
    <link rel="alternate icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ url_for('static', filename='images/branding/favicon.svg') }}">
    <link rel="manifest" href="{{ url_for('static', filename='site.webmanifest') }}">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-navy: #1e3a8a;
            --primary-gold: #d4af37;
            --light-gray: #f8f9fa;
            --dark-gray: #6b7280;
        }

        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            direction: rtl;
            text-align: right;
            line-height: 1.6;
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-navy) !important;
        }

        .text-primary-custom { color: var(--primary-navy) !important; }
        .bg-primary-custom { background-color: var(--primary-navy) !important; }
        .text-gold { color: var(--primary-gold) !important; }
        .bg-gold { background-color: var(--primary-gold) !important; }

        .btn-primary-custom {
            background-color: var(--primary-navy);
            border-color: var(--primary-navy);
            color: white;
        }

        .btn-primary-custom:hover {
            background-color: #1e40af;
            border-color: #1e40af;
            color: white;
        }

        .btn-gold {
            background-color: var(--primary-gold);
            border-color: var(--primary-gold);
            color: white;
        }

        .btn-gold:hover {
            background-color: #b8941f;
            border-color: #b8941f;
            color: white;
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .search-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 0.375rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
        }

        .search-item {
            padding: 0.75rem;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .search-item:hover {
            background-color: #f8f9fa;
        }

        .search-item:last-child {
            border-bottom: none;
        }

        .breadcrumb-item + .breadcrumb-item::before {
            content: "‹";
            float: left;
            padding-right: 0.5rem;
            padding-left: 0;
        }

        .article-meta {
            font-size: 0.875rem;
            color: var(--dark-gray);
        }

        .view-count {
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .loading-spinner {
            display: none;
        }

        .loading .loading-spinner {
            display: inline-block;
        }

        .loading .loading-text {
            display: none;
        }

        @media (max-width: 768px) {
            .display-4 { font-size: 2rem; }
            .lead { font-size: 1rem; }
            .btn-lg { padding: 0.75rem 1.5rem; font-size: 1rem; }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg bg-white shadow-sm sticky-top">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="{{ url_for('index') }}">
                <img src="{{ url_for('static', filename='images/branding/logo-simple.svg') }}"
                     alt="{{ site_settings.site_name }}"
                     height="40"
                     class="me-2">
                <span class="fs-4 fw-bold text-primary-custom d-none d-md-inline">{{ site_settings.site_name }}</span>
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'index' %}active{% endif %}" href="{{ url_for('index') }}">
                            <i class="bi bi-house me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'articles' %}active{% endif %}" href="{{ url_for('articles') }}">
                            <i class="bi bi-journal-text me-1"></i>المقالات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'jurisprudence' %}active{% endif %}" href="{{ url_for('jurisprudence') }}">
                            <i class="bi bi-bank me-1"></i>الاجتهادات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'consultation' %}active{% endif %}" href="{{ url_for('consultation') }}">
                            <i class="bi bi-chat-dots me-1"></i>استشارة قانونية
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-info-circle me-1"></i>المزيد
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('about') }}">من نحن</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('contact') }}">اتصل بنا</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#">سياسة الخصوصية</a></li>
                        </ul>
                    </li>
                </ul>

                <!-- Search Form -->
                <div class="position-relative me-3" style="width: 300px;">
                    <form class="d-flex" role="search" id="searchForm">
                        <div class="input-group">
                            <input class="form-control" type="search" placeholder="البحث في المقالات..."
                                   id="searchInput" autocomplete="off">
                            <button class="btn btn-outline-primary" type="submit">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </form>
                    <div id="searchResults" class="search-dropdown" style="display: none;"></div>
                </div>

                {% if current_user and current_user.is_admin %}
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('logout') }}">
                            <i class="bi bi-box-arrow-right me-1"></i>تسجيل الخروج
                        </a>
                    </li>
                </ul>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Breadcrumb -->
    {% block breadcrumb %}{% endblock %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Back to Top Button -->
    <button id="backToTop" class="btn btn-primary-custom position-fixed"
            style="bottom: 20px; left: 20px; display: none; z-index: 1000;">
        <i class="bi bi-arrow-up"></i>
    </button>

    <!-- Footer -->
    <footer class="bg-primary-custom text-white mt-5">
        <div class="container py-5">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="d-flex align-items-center mb-3">
                        <img src="{{ url_for('static', filename='images/branding/logo.svg') }}"
                             alt="{{ site_settings.site_name }}"
                             height="50"
                             class="me-3">
                    </div>
                    <p class="mb-3">{{ site_settings.site_description }}</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-white fs-4"><i class="bi bi-facebook"></i></a>
                        <a href="#" class="text-white fs-4"><i class="bi bi-twitter"></i></a>
                        <a href="#" class="text-white fs-4"><i class="bi bi-linkedin"></i></a>
                        <a href="#" class="text-white fs-4"><i class="bi bi-envelope"></i></a>
                    </div>
                </div>

                <div class="col-lg-2 mb-4">
                    <h6 class="fw-bold mb-3">روابط سريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="{{ url_for('index') }}" class="text-white-50 text-decoration-none">الرئيسية</a></li>
                        <li><a href="{{ url_for('articles') }}" class="text-white-50 text-decoration-none">المقالات</a></li>
                        <li><a href="{{ url_for('jurisprudence') }}" class="text-white-50 text-decoration-none">الاجتهادات</a></li>
                        <li><a href="{{ url_for('consultation') }}" class="text-white-50 text-decoration-none">استشارة قانونية</a></li>
                    </ul>
                </div>

                <div class="col-lg-3 mb-4">
                    <h6 class="fw-bold mb-3">معلومات الاتصال</h6>
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="bi bi-geo-alt me-2"></i>{{ site_settings.address }}</li>
                        <li class="mb-2"><i class="bi bi-telephone me-2"></i>{{ site_settings.contact_phone }}</li>
                        <li class="mb-2"><i class="bi bi-envelope me-2"></i>{{ site_settings.contact_email }}</li>
                    </ul>
                </div>

                <div class="col-lg-3 mb-4">
                    <h6 class="fw-bold mb-3">النشرة الإخبارية</h6>
                    <p class="small mb-3">اشترك للحصول على آخر التحديثات القانونية</p>
                    <form class="d-flex">
                        <input type="email" class="form-control me-2" placeholder="بريدك الإلكتروني">
                        <button class="btn btn-gold" type="submit">اشتراك</button>
                    </form>
                </div>
            </div>

            <hr class="my-4">

            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">&copy; 2024 {{ site_settings.site_name }}. جميع الحقوق محفوظة.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-white-50 text-decoration-none me-3">سياسة الخصوصية</a>
                    <a href="#" class="text-white-50 text-decoration-none">شروط الاستخدام</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Search functionality
        let searchTimeout;
        const searchInput = document.getElementById('searchInput');
        const searchResults = document.getElementById('searchResults');

        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length >= 2) {
                searchTimeout = setTimeout(() => {
                    fetch(`/api/search?q=${encodeURIComponent(query)}`)
                        .then(response => response.json())
                        .then(data => {
                            showSearchResults(data.results);
                        })
                        .catch(error => console.error('Search error:', error));
                }, 300);
            } else {
                hideSearchResults();
            }
        });

        function showSearchResults(results) {
            if (results.length > 0) {
                searchResults.innerHTML = results.map(result => `
                    <div class="search-item" onclick="window.location.href='${result.url}'">
                        <div class="fw-bold">${result.title}</div>
                        <div class="text-muted small">${result.excerpt}</div>
                        <div class="text-primary small">${result.category}</div>
                    </div>
                `).join('');
                searchResults.style.display = 'block';
            } else {
                searchResults.innerHTML = '<div class="search-item text-muted">لا توجد نتائج</div>';
                searchResults.style.display = 'block';
            }
        }

        function hideSearchResults() {
            searchResults.style.display = 'none';
        }

        // Hide search results when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.position-relative')) {
                hideSearchResults();
            }
        });

        // Search form submission
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const query = searchInput.value.trim();
            if (query) {
                window.location.href = `{{ url_for('articles') }}?search=${encodeURIComponent(query)}`;
            }
        });

        // Back to top button
        const backToTopButton = document.getElementById('backToTop');
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopButton.style.display = 'block';
            } else {
                backToTopButton.style.display = 'none';
            }
        });

        backToTopButton.addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
