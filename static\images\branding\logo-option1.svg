<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 220 70" width="220" height="70">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#C9B037;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8860B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="navyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1a365d;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2c5282;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main Background -->
  <rect width="220" height="70" rx="12" fill="white" stroke="url(#navyGradient)" stroke-width="2"/>
  
  <!-- Icon Background Circle -->
  <circle cx="35" cy="35" r="25" fill="url(#navyGradient)"/>
  
  <!-- Arabic Calligraphy Style Icon -->
  <g transform="translate(35, 35)">
    <!-- Stylized Arabic Letter ق -->
    <path d="M -8,-12 Q -8,-18 0,-18 Q 8,-18 8,-12 Q 8,-6 0,-6 Q -8,-6 -8,-12 Z" 
          fill="url(#goldGradient)" stroke="none"/>
    <rect x="-2" y="-6" width="4" height="18" fill="url(#goldGradient)"/>
    <circle cx="0" cy="8" r="3" fill="url(#goldGradient)"/>
    
    <!-- Decorative dots -->
    <circle cx="-12" cy="-8" r="1.5" fill="url(#goldGradient)" opacity="0.7"/>
    <circle cx="12" cy="-8" r="1.5" fill="url(#goldGradient)" opacity="0.7"/>
  </g>
  
  <!-- Arabic Text -->
  <text x="75" y="30" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="url(#navyGradient)">
    قانوني
  </text>
  <text x="75" y="48" font-family="Arial, sans-serif" font-size="12" fill="url(#navyGradient)" opacity="0.8">
    موقع قانوني متخصص
  </text>
  <text x="75" y="62" font-family="Arial, sans-serif" font-size="9" fill="url(#navyGradient)" opacity="0.6">
    Legal Expert Platform
  </text>
  
  <!-- Decorative Border -->
  <rect x="3" y="3" width="214" height="64" rx="9" fill="none" stroke="url(#goldGradient)" stroke-width="1" opacity="0.3"/>
</svg>
