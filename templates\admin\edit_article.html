{% extends "admin/base.html" %}

{% block title %}تحرير المقال{% endblock %}
{% block page_title %}تحرير المقال: {{ article.title_ar }}{% endblock %}

{% block page_actions %}
<div class="btn-group">
    {% if article.is_published %}
    <a href="{{ url_for('article_detail', slug=article.slug) }}" class="btn btn-outline-info" target="_blank">
        <i class="bi bi-eye me-1"></i>
        عرض المقال
    </a>
    {% endif %}
    <a href="{{ url_for('admin_articles') }}" class="btn btn-outline-secondary">
        <i class="bi bi-arrow-right me-1"></i>
        العودة للمقالات
    </a>
</div>
{% endblock %}

{% block content %}
<form method="POST" id="articleForm" enctype="multipart/form-data">
    <div class="row">
        <div class="col-lg-8">
            <!-- Basic Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        المعلومات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="title_ar" class="form-label fw-bold">العنوان بالعربية *</label>
                        <input type="text" class="form-control" id="title_ar" name="title_ar"
                               value="{{ article.title_ar }}" required>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label fw-bold">العنوان بالإنجليزية</label>
                        <input type="text" class="form-control" id="title" name="title"
                               value="{{ article.title or '' }}">
                        <div class="form-text">اختياري - للاستخدام في URLs</div>
                    </div>

                    <div class="mb-3">
                        <label for="excerpt" class="form-label fw-bold">الملخص *</label>
                        <textarea class="form-control" id="excerpt" name="excerpt" rows="3" required>{{ article.excerpt }}</textarea>
                        <div class="form-text">ملخص قصير للمقال (يظهر في قوائم المقالات)</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="category" class="form-label fw-bold">التصنيف *</label>
                            <select class="form-select" id="category" name="category" required>
                                <option value="">اختر التصنيف</option>
                                {% for cat in categories %}
                                <option value="{{ cat }}" {% if cat == article.category %}selected{% endif %}>{{ cat }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="tags" class="form-label fw-bold">العلامات</label>
                            <input type="text" class="form-control" id="tags" name="tags"
                                   value="{{ article.tags|join(', ') if article.tags else '' }}"
                                   placeholder="علامة1, علامة2, علامة3">
                            <div class="form-text">افصل بين العلامات بفاصلة</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Featured Image -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-image me-2"></i>
                        الصورة المميزة
                    </h5>
                </div>
                <div class="card-body">
                    {% if article.featured_image %}
                    <div class="mb-3">
                        <label class="form-label fw-bold">الصورة الحالية:</label>
                        <div class="border rounded p-2 mb-3">
                            <img src="{{ url_for('static', filename='uploads/articles/' + article.featured_image) }}"
                                 alt="الصورة المميزة" class="img-fluid rounded" style="max-height: 200px;">
                        </div>
                        <form method="POST" action="{{ url_for('admin_delete_article_image', article_id=article.id) }}"
                              class="d-inline" onsubmit="return confirm('هل تريد حذف الصورة؟')">
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="bi bi-trash me-1"></i>حذف الصورة
                            </button>
                        </form>
                    </div>
                    {% endif %}

                    <div class="mb-3">
                        <label for="featured_image" class="form-label fw-bold">
                            {% if article.featured_image %}تغيير الصورة{% else %}رفع صورة مميزة{% endif %}
                        </label>
                        <input type="file" class="form-control" id="featured_image" name="featured_image"
                               accept="image/*" onchange="previewImage(this)">
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            الصيغ المدعومة: JPG, PNG, GIF, WEBP - الحد الأقصى: 5 ميجابايت
                        </div>
                    </div>

                    <!-- Image Preview -->
                    <div id="imagePreview" class="mt-3" style="display: none;">
                        <label class="form-label fw-bold">معاينة الصورة الجديدة:</label>
                        <div class="border rounded p-2">
                            <img id="previewImg" src="" alt="معاينة الصورة"
                                 class="img-fluid rounded" style="max-height: 200px;">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-file-text me-2"></i>
                        محتوى المقال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="content" class="form-label fw-bold">المحتوى *</label>
                        <textarea class="form-control" id="content" name="content" rows="15" required>{{ article.content }}</textarea>
                        <div class="form-text">يمكنك استخدام HTML للتنسيق</div>
                    </div>
                </div>
            </div>

            <!-- SEO Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-search me-2"></i>
                        إعدادات SEO
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="meta_title" class="form-label fw-bold">عنوان SEO</label>
                        <input type="text" class="form-control" id="meta_title" name="meta_title"
                               value="{{ article.meta_title or '' }}">
                        <div class="form-text">إذا ترك فارغاً، سيتم استخدام العنوان الأساسي</div>
                    </div>

                    <div class="mb-3">
                        <label for="meta_description" class="form-label fw-bold">وصف SEO</label>
                        <textarea class="form-control" id="meta_description" name="meta_description" rows="2">{{ article.meta_description or '' }}</textarea>
                        <div class="form-text">وصف قصير للمقال (يظهر في نتائج البحث)</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Article Info -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info me-2"></i>
                        معلومات المقال
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        <strong>تاريخ الإنشاء:</strong>
                        <div class="text-muted">{{ article.created_at[:10] }}</div>
                    </div>
                    {% if article.published_at %}
                    <div class="mb-2">
                        <strong>تاريخ النشر:</strong>
                        <div class="text-muted">{{ article.published_at[:10] }}</div>
                    </div>
                    {% endif %}
                    <div class="mb-2">
                        <strong>المشاهدات:</strong>
                        <div class="text-muted">{{ article.views or 0 }}</div>
                    </div>
                    <div class="mb-2">
                        <strong>الرابط:</strong>
                        <div class="text-muted small">{{ article.slug }}</div>
                    </div>
                </div>
            </div>

            <!-- Publish Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-gear me-2"></i>
                        إعدادات النشر
                    </h5>
                </div>
                <div class="card-body">
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_published" name="is_published"
                               {% if article.is_published %}checked{% endif %}>
                        <label class="form-check-label fw-bold" for="is_published">
                            نشر المقال
                        </label>
                        <div class="form-text">إذا لم يتم تحديده، سيتم حفظه كمسودة</div>
                    </div>

                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured"
                               {% if article.is_featured %}checked{% endif %}>
                        <label class="form-check-label fw-bold" for="is_featured">
                            مقال مميز
                        </label>
                        <div class="form-text">سيظهر في قسم المقالات المميزة</div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card">
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary-custom btn-lg">
                            <i class="bi bi-check-circle me-2"></i>
                            حفظ التغييرات
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="previewArticle()">
                            <i class="bi bi-eye me-2"></i>
                            معاينة
                        </button>
                        {% if article.is_published %}
                        <a href="{{ url_for('article_detail', slug=article.slug) }}"
                           class="btn btn-outline-info" target="_blank">
                            <i class="bi bi-globe me-2"></i>
                            عرض في الموقع
                        </a>
                        {% endif %}
                        <a href="{{ url_for('admin_articles') }}" class="btn btn-outline-danger">
                            <i class="bi bi-x-circle me-2"></i>
                            إلغاء
                        </a>
                    </div>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="card mt-4 border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        منطقة خطر
                    </h6>
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">حذف المقال نهائياً - لا يمكن التراجع عن هذا الإجراء</p>
                    <form method="POST" action="{{ url_for('admin_delete_article', article_id=article.id) }}"
                          onsubmit="return confirm('هل أنت متأكد من حذف هذا المقال نهائياً؟ لا يمكن التراجع عن هذا الإجراء.')">
                        <button type="submit" class="btn btn-danger btn-sm">
                            <i class="bi bi-trash me-1"></i>
                            حذف المقال
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة المقال</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="previewContent">
                <!-- Preview content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function previewImage(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Check file size (5MB limit)
        if (file.size > 5 * 1024 * 1024) {
            alert('حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت.');
            input.value = '';
            document.getElementById('imagePreview').style.display = 'none';
            return;
        }

        // Check file type
        if (!file.type.startsWith('image/')) {
            alert('يرجى اختيار ملف صورة صحيح.');
            input.value = '';
            document.getElementById('imagePreview').style.display = 'none';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImg').src = e.target.result;
            document.getElementById('imagePreview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        document.getElementById('imagePreview').style.display = 'none';
    }
}

function previewArticle() {
    const title = document.getElementById('title_ar').value;
    const content = document.getElementById('content').value;
    const excerpt = document.getElementById('excerpt').value;
    const category = document.getElementById('category').value;

    if (!title || !content) {
        alert('يرجى ملء العنوان والمحتوى أولاً');
        return;
    }

    const previewHTML = `
        <div class="article-preview">
            <div class="mb-3">
                <span class="badge bg-primary">${category}</span>
            </div>
            <h1 class="display-6 fw-bold text-primary mb-3">${title}</h1>
            <p class="lead text-muted mb-4">${excerpt}</p>
            <div class="article-content">
                ${content}
            </div>
        </div>
    `;

    document.getElementById('previewContent').innerHTML = previewHTML;
    new bootstrap.Modal(document.getElementById('previewModal')).show();
}

// Character counter for excerpt
const excerptTextarea = document.getElementById('excerpt');
const excerptCounter = document.createElement('div');
excerptCounter.className = 'form-text text-end';
excerptTextarea.parentNode.appendChild(excerptCounter);

excerptTextarea.addEventListener('input', function() {
    const length = this.value.length;
    excerptCounter.textContent = `${length} حرف`;

    if (length > 200) {
        excerptCounter.className = 'form-text text-end text-warning';
        excerptCounter.textContent += ' - طويل جداً';
    } else if (length < 50) {
        excerptCounter.className = 'form-text text-end text-muted';
        excerptCounter.textContent += ' - قصير';
    } else {
        excerptCounter.className = 'form-text text-end text-success';
        excerptCounter.textContent += ' - مناسب';
    }
});

// Trigger initial counter
excerptTextarea.dispatchEvent(new Event('input'));
</script>
{% endblock %}
