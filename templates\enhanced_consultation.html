{% extends "enhanced_base.html" %}

{% block title %}استشارة قانونية{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb" class="bg-light py-2">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
            <li class="breadcrumb-item active">استشارة قانونية</li>
        </ol>
    </div>
</nav>
{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="bg-primary-custom text-white py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="display-5 fw-bold mb-2">استشارة قانونية</h1>
                <p class="lead mb-0">احصل على استشارة قانونية متخصصة من خبرائنا</p>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Info Section -->
            <div class="alert alert-info mb-4" role="alert">
                <h5 class="alert-heading">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات مهمة
                </h5>
                <p class="mb-0">
                    نحن نقدم استشارات قانونية متخصصة في جميع المجالات القانونية. 
                    يرجى ملء النموذج أدناه بالتفاصيل الكاملة لحالتك وسنتواصل معك في أقرب وقت ممكن.
                </p>
            </div>

            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h4 class="fw-bold text-primary-custom mb-4">
                        <i class="bi bi-chat-dots me-2"></i>
                        نموذج طلب الاستشارة
                    </h4>
                    
                    <form method="POST" enctype="multipart/form-data" id="consultationForm">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label fw-bold">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label fw-bold">البريد الإلكتروني *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label fw-bold">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       placeholder="+212 123 456 789">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="legal_field" class="form-label fw-bold">المجال القانوني</label>
                                <select class="form-select" id="legal_field" name="legal_field">
                                    <option value="">اختر المجال</option>
                                    <option value="civil">القانون المدني</option>
                                    <option value="commercial">القانون التجاري</option>
                                    <option value="criminal">القانون الجنائي</option>
                                    <option value="family">قانون الأسرة</option>
                                    <option value="labor">قانون العمل</option>
                                    <option value="administrative">القانون الإداري</option>
                                    <option value="real_estate">القانون العقاري</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="subject" class="form-label fw-bold">موضوع الاستشارة *</label>
                            <input type="text" class="form-control" id="subject" name="subject" required 
                                   placeholder="عنوان مختصر لموضوع الاستشارة">
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label fw-bold">تفاصيل الاستشارة *</label>
                            <textarea class="form-control" id="message" name="message" rows="6" required 
                                      placeholder="اشرح حالتك بالتفصيل مع ذكر جميع المعلومات المهمة والظروف المحيطة بالقضية"></textarea>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                كلما كانت التفاصيل أكثر دقة، كانت الاستشارة أكثر فائدة
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="attachment" class="form-label fw-bold">مرفقات (اختياري)</label>
                            <input type="file" class="form-control" id="attachment" name="attachment" 
                                   accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif">
                            <div class="form-text">
                                يمكنك إرفاق المستندات ذات الصلة (PDF, Word, صور) - الحد الأقصى 16 ميجابايت
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">درجة الأولوية</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="priority" id="priority_normal" value="normal" checked>
                                        <label class="form-check-label" for="priority_normal">
                                            <span class="badge bg-success me-2">عادية</span>
                                            خلال 48 ساعة
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="priority" id="priority_urgent" value="urgent">
                                        <label class="form-check-label" for="priority_urgent">
                                            <span class="badge bg-warning me-2">عاجلة</span>
                                            خلال 24 ساعة
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="priority" id="priority_emergency" value="emergency">
                                        <label class="form-check-label" for="priority_emergency">
                                            <span class="badge bg-danger me-2">طارئة</span>
                                            خلال 6 ساعات
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="privacy_agreement" required>
                            <label class="form-check-label" for="privacy_agreement">
                                أوافق على <a href="#" class="text-primary" data-bs-toggle="modal" data-bs-target="#privacyModal">سياسة الخصوصية</a> وشروط الاستخدام *
                            </label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary-custom btn-lg">
                                <span class="loading-text">
                                    <i class="bi bi-send me-2"></i>
                                    إرسال طلب الاستشارة
                                </span>
                                <span class="loading-spinner spinner-border spinner-border-sm me-2" role="status">
                                    <span class="visually-hidden">جاري الإرسال...</span>
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Contact Info -->
            <div class="row mt-5">
                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm text-center h-100">
                        <div class="card-body">
                            <i class="bi bi-telephone text-primary-custom display-6 mb-3"></i>
                            <h6 class="fw-bold">اتصل بنا</h6>
                            <p class="text-muted small">{{ site_settings.contact_phone }}</p>
                            <small class="text-muted">الأحد - الخميس: 9:00 - 17:00</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm text-center h-100">
                        <div class="card-body">
                            <i class="bi bi-envelope text-primary-custom display-6 mb-3"></i>
                            <h6 class="fw-bold">راسلنا</h6>
                            <p class="text-muted small">{{ site_settings.contact_email }}</p>
                            <small class="text-muted">رد خلال 24 ساعة</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card border-0 shadow-sm text-center h-100">
                        <div class="card-body">
                            <i class="bi bi-chat-dots text-primary-custom display-6 mb-3"></i>
                            <h6 class="fw-bold">استشارة مجانية</h6>
                            <p class="text-muted small">الاستشارة الأولى مجانية</p>
                            <small class="text-muted">لجميع العملاء الجدد</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Policy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">سياسة الخصوصية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>حماية البيانات الشخصية</h6>
                <p>نحن نلتزم بحماية خصوصيتك وبياناتك الشخصية وفقاً لأعلى معايير الأمان.</p>
                
                <h6>استخدام المعلومات</h6>
                <p>سيتم استخدام المعلومات المقدمة فقط لغرض تقديم الاستشارة القانونية المطلوبة.</p>
                
                <h6>سرية المعلومات</h6>
                <p>جميع المعلومات المقدمة محمية بسرية تامة ولن يتم مشاركتها مع أطراف ثالثة.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">فهمت</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.getElementById('consultationForm').addEventListener('submit', function(e) {
    const submitBtn = this.querySelector('button[type="submit"]');
    submitBtn.classList.add('loading');
    submitBtn.disabled = true;
    
    // Re-enable after 10 seconds as fallback
    setTimeout(() => {
        submitBtn.classList.remove('loading');
        submitBtn.disabled = false;
    }, 10000);
});

// File size validation
document.getElementById('attachment').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file && file.size > 16 * 1024 * 1024) { // 16MB
        alert('حجم الملف كبير جداً. الحد الأقصى 16 ميجابايت.');
        this.value = '';
    }
});

// Character counter for message
const messageTextarea = document.getElementById('message');
const charCounter = document.createElement('div');
charCounter.className = 'form-text text-end';
messageTextarea.parentNode.appendChild(charCounter);

messageTextarea.addEventListener('input', function() {
    const length = this.value.length;
    charCounter.textContent = `${length} حرف`;
    
    if (length < 50) {
        charCounter.className = 'form-text text-end text-warning';
        charCounter.textContent += ' - يُنصح بكتابة تفاصيل أكثر';
    } else {
        charCounter.className = 'form-text text-end text-success';
    }
});
</script>
{% endblock %}
