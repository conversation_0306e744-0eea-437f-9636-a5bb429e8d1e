{% extends "admin/base.html" %}

{% block title %}إدارة الاجتهادات{% endblock %}
{% block page_title %}إدارة الاجتهادات المغربية{% endblock %}

{% block page_actions %}
<a href="{{ url_for('admin_new_jurisprudence') }}" class="btn btn-primary-custom">
    <i class="bi bi-plus-circle me-1"></i>
    اجتهاد جديد
</a>
{% endblock %}

{% block content %}
<!-- Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-8">
                <label for="search" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="{{ current_search }}" placeholder="ابحث في العناوين والملخصات...">
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="bi bi-search me-1"></i>بحث
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Jurisprudence Table -->
<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="bi bi-bank me-2 text-primary-custom"></i>
            قائمة الاجتهادات ({{ cases|length }})
        </h5>
    </div>
    <div class="card-body p-0">
        {% if cases %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="bg-light">
                    <tr>
                        <th class="px-4 py-3">العنوان</th>
                        <th class="px-4 py-3">رقم القضية</th>
                        <th class="px-4 py-3">المحكمة</th>
                        <th class="px-4 py-3">التصنيف</th>
                        <th class="px-4 py-3">التاريخ</th>
                        <th class="px-4 py-3">الحالة</th>
                        <th class="px-4 py-3">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for case in cases %}
                    <tr>
                        <td class="px-4 py-3">
                            <div>
                                <h6 class="mb-1 fw-bold">{{ case.title }}</h6>
                                <small class="text-muted">{{ case.summary[:80] }}{% if case.summary|length > 80 %}...{% endif %}</small>
                            </div>
                        </td>
                        <td class="px-4 py-3">
                            <code class="bg-light px-2 py-1 rounded">{{ case.case_number }}</code>
                        </td>
                        <td class="px-4 py-3">
                            <span class="badge bg-gold">{{ case.court }}</span>
                        </td>
                        <td class="px-4 py-3">
                            <span class="badge bg-primary-custom">{{ case.category }}</span>
                        </td>
                        <td class="px-4 py-3">
                            <small class="text-muted">{{ case.date }}</small>
                        </td>
                        <td class="px-4 py-3">
                            {% if case.is_published %}
                                <span class="badge bg-success">منشور</span>
                            {% else %}
                                <span class="badge bg-secondary">مسودة</span>
                            {% endif %}
                        </td>
                        <td class="px-4 py-3">
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-info" onclick="viewCase({{ case.id }})" title="عرض">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <a href="{{ url_for('admin_edit_jurisprudence', case_id=case.id) }}"
                                   class="btn btn-outline-primary" title="تحرير">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <form method="POST" action="{{ url_for('admin_delete_jurisprudence', case_id=case.id) }}"
                                      class="d-inline">
                                    <button type="submit" class="btn btn-outline-danger btn-delete" title="حذف">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-bank display-1 text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد اجتهادات</h4>
            {% if current_search %}
            <p class="text-muted">لم يتم العثور على اجتهادات تطابق معايير البحث</p>
            <a href="{{ url_for('admin_jurisprudence') }}" class="btn btn-outline-primary">
                <i class="bi bi-arrow-clockwise me-1"></i>
                إزالة البحث
            </a>
            {% else %}
            <p class="text-muted">ابدأ بإضافة اجتهاد جديد</p>
            <a href="{{ url_for('admin_new_jurisprudence') }}" class="btn btn-primary-custom">
                <i class="bi bi-plus-circle me-1"></i>
                إضافة اجتهاد جديد
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
{% if cases %}
<div class="row mt-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-bank display-4 mb-2"></i>
                <h4 class="fw-bold">{{ cases|length }}</h4>
                <small>إجمالي الاجتهادات</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card success">
            <div class="card-body text-center">
                <i class="bi bi-check-circle display-4 mb-2"></i>
                <h4 class="fw-bold">
                    {% set published_count = 0 %}
                    {% for case in cases %}
                        {% if case.is_published %}
                            {% set published_count = published_count + 1 %}
                        {% endif %}
                    {% endfor %}
                    {{ published_count }}
                </h4>
                <small>منشور</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card warning">
            <div class="card-body text-center">
                <i class="bi bi-file-earmark display-4 mb-2"></i>
                <h4 class="fw-bold">
                    {% set draft_count = 0 %}
                    {% for case in cases %}
                        {% if not case.is_published %}
                            {% set draft_count = draft_count + 1 %}
                        {% endif %}
                    {% endfor %}
                    {{ draft_count }}
                </h4>
                <small>مسودة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card gold">
            <div class="card-body text-center">
                <i class="bi bi-calendar3 display-4 mb-2"></i>
                <h4 class="fw-bold">
                    {% set current_year_cases = [] %}
                    {% for case in cases %}
                        {% if case.date and case.date[:4] == '2024' %}
                            {% set _ = current_year_cases.append(case) %}
                        {% endif %}
                    {% endfor %}
                    {{ current_year_cases|length }}
                </h4>
                <small>هذا العام</small>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Case Details Modal -->
<div class="modal fade" id="caseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل الاجتهاد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="caseDetails">
                <!-- Case details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function viewCase(caseId) {
    // Find the case data from the table
    const cases = {{ cases|tojson }};
    const caseData = cases.find(c => c.id === caseId);

    if (caseData) {
        const detailsHTML = `
            <div class="case-details">
                <div class="row mb-3">
                    <div class="col-sm-3 fw-bold">العنوان:</div>
                    <div class="col-sm-9">${caseData.title}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 fw-bold">رقم القضية:</div>
                    <div class="col-sm-9"><code>${caseData.case_number}</code></div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 fw-bold">المحكمة:</div>
                    <div class="col-sm-9"><span class="badge bg-gold">${caseData.court}</span></div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 fw-bold">التاريخ:</div>
                    <div class="col-sm-9">${caseData.date}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 fw-bold">التصنيف:</div>
                    <div class="col-sm-9"><span class="badge bg-primary">${caseData.category}</span></div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 fw-bold">الملخص:</div>
                    <div class="col-sm-9">
                        <div class="bg-light p-3 rounded">${caseData.summary}</div>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3 fw-bold">المبدأ القانوني:</div>
                    <div class="col-sm-9">
                        <div class="bg-primary bg-opacity-10 p-3 rounded border-start border-primary border-3">
                            <strong>${caseData.legal_principle}</strong>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-3 fw-bold">الحالة:</div>
                    <div class="col-sm-9">
                        ${caseData.is_published ? '<span class="badge bg-success">منشور</span>' : '<span class="badge bg-secondary">مسودة</span>'}
                    </div>
                </div>
            </div>
        `;

        document.getElementById('caseDetails').innerHTML = detailsHTML;
        new bootstrap.Modal(document.getElementById('caseModal')).show();
    }
}
</script>
{% endblock %}
