"""
Security Monitoring System
Real-time security monitoring and threat detection
"""

import logging
import json
import os
import time
from datetime import datetime, timedelta
from collections import defaultdict, deque
from threading import Lock
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

class SecurityMonitor:
    """Real-time security monitoring system"""
    
    def __init__(self, config=None):
        self.config = config or {}
        self.events = deque(maxlen=10000)  # Store last 10k events
        self.ip_attempts = defaultdict(list)  # Track attempts by IP
        self.user_attempts = defaultdict(list)  # Track attempts by user
        self.suspicious_ips = set()
        self.blocked_ips = set()
        self.lock = Lock()
        
        # Setup logging
        self.setup_logging()
        
        # Load existing data
        self.load_security_data()
    
    def setup_logging(self):
        """Setup security logging"""
        log_dir = 'logs'
        os.makedirs(log_dir, exist_ok=True)
        
        # Security logger
        self.security_logger = logging.getLogger('security_monitor')
        self.security_logger.setLevel(logging.INFO)
        
        # File handler
        file_handler = logging.FileHandler(f'{log_dir}/security_monitor.log')
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.security_logger.addHandler(file_handler)
        self.security_logger.addHandler(console_handler)
    
    def load_security_data(self):
        """Load existing security data"""
        try:
            if os.path.exists('security_data.json'):
                with open('security_data.json', 'r') as f:
                    data = json.load(f)
                    self.blocked_ips = set(data.get('blocked_ips', []))
                    self.suspicious_ips = set(data.get('suspicious_ips', []))
        except Exception as e:
            self.security_logger.error(f"Failed to load security data: {e}")
    
    def save_security_data(self):
        """Save security data"""
        try:
            data = {
                'blocked_ips': list(self.blocked_ips),
                'suspicious_ips': list(self.suspicious_ips),
                'last_updated': datetime.now().isoformat()
            }
            with open('security_data.json', 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            self.security_logger.error(f"Failed to save security data: {e}")
    
    def log_event(self, event_type, user_id=None, ip_address=None, details=None, severity='INFO'):
        """Log security event"""
        with self.lock:
            event = {
                'timestamp': datetime.now().isoformat(),
                'type': event_type,
                'user_id': user_id,
                'ip_address': ip_address,
                'details': details or {},
                'severity': severity
            }
            
            self.events.append(event)
            
            # Log to file
            log_message = f"{event_type} | User: {user_id} | IP: {ip_address} | Details: {details}"
            
            if severity == 'CRITICAL':
                self.security_logger.critical(log_message)
            elif severity == 'WARNING':
                self.security_logger.warning(log_message)
            else:
                self.security_logger.info(log_message)
            
            # Analyze event
            self.analyze_event(event)
    
    def analyze_event(self, event):
        """Analyze security event for threats"""
        event_type = event['type']
        ip_address = event['ip_address']
        user_id = event['user_id']
        
        # Track failed login attempts
        if event_type == 'login_failed':
            self.track_failed_login(ip_address, user_id)
        
        # Track suspicious activities
        elif event_type in ['suspicious_request', 'csrf_violation', 'rate_limit_exceeded']:
            self.track_suspicious_activity(ip_address)
        
        # Track file upload attempts
        elif event_type == 'file_upload_rejected':
            self.track_file_upload_attempt(ip_address)
        
        # Check for brute force attacks
        self.check_brute_force(ip_address)
        
        # Check for distributed attacks
        self.check_distributed_attack()
    
    def track_failed_login(self, ip_address, user_id):
        """Track failed login attempts"""
        now = datetime.now()
        
        # Track by IP
        if ip_address:
            self.ip_attempts[ip_address].append(now)
            # Keep only last hour
            self.ip_attempts[ip_address] = [
                t for t in self.ip_attempts[ip_address] 
                if now - t < timedelta(hours=1)
            ]
        
        # Track by user
        if user_id:
            self.user_attempts[user_id].append(now)
            # Keep only last hour
            self.user_attempts[user_id] = [
                t for t in self.user_attempts[user_id] 
                if now - t < timedelta(hours=1)
            ]
    
    def track_suspicious_activity(self, ip_address):
        """Track suspicious activities"""
        if ip_address:
            self.suspicious_ips.add(ip_address)
            self.log_event(
                'suspicious_ip_detected',
                ip_address=ip_address,
                details={'reason': 'Multiple suspicious activities'},
                severity='WARNING'
            )
    
    def track_file_upload_attempt(self, ip_address):
        """Track malicious file upload attempts"""
        if ip_address:
            # Add to suspicious IPs after multiple rejected uploads
            recent_uploads = [
                event for event in self.events
                if event['type'] == 'file_upload_rejected' 
                and event['ip_address'] == ip_address
                and datetime.now() - datetime.fromisoformat(event['timestamp']) < timedelta(minutes=10)
            ]
            
            if len(recent_uploads) >= 3:
                self.suspicious_ips.add(ip_address)
                self.log_event(
                    'malicious_upload_pattern',
                    ip_address=ip_address,
                    details={'rejected_uploads': len(recent_uploads)},
                    severity='WARNING'
                )
    
    def check_brute_force(self, ip_address):
        """Check for brute force attacks"""
        if not ip_address:
            return
        
        attempts = len(self.ip_attempts.get(ip_address, []))
        
        # Block IP after 10 failed attempts in 1 hour
        if attempts >= 10:
            self.block_ip(ip_address, 'brute_force_attack')
        
        # Warn after 5 failed attempts
        elif attempts >= 5:
            self.log_event(
                'potential_brute_force',
                ip_address=ip_address,
                details={'attempts': attempts},
                severity='WARNING'
            )
    
    def check_distributed_attack(self):
        """Check for distributed attacks"""
        now = datetime.now()
        recent_events = [
            event for event in self.events
            if now - datetime.fromisoformat(event['timestamp']) < timedelta(minutes=5)
        ]
        
        # Check for high volume of failed logins from different IPs
        failed_logins = [
            event for event in recent_events
            if event['type'] == 'login_failed'
        ]
        
        unique_ips = set(event['ip_address'] for event in failed_logins if event['ip_address'])
        
        if len(failed_logins) >= 20 and len(unique_ips) >= 5:
            self.log_event(
                'distributed_attack_detected',
                details={
                    'failed_logins': len(failed_logins),
                    'unique_ips': len(unique_ips)
                },
                severity='CRITICAL'
            )
            
            # Send alert
            self.send_security_alert('Distributed Attack Detected', {
                'failed_logins': len(failed_logins),
                'unique_ips': len(unique_ips),
                'time_window': '5 minutes'
            })
    
    def block_ip(self, ip_address, reason):
        """Block IP address"""
        self.blocked_ips.add(ip_address)
        self.save_security_data()
        
        self.log_event(
            'ip_blocked',
            ip_address=ip_address,
            details={'reason': reason},
            severity='CRITICAL'
        )
        
        # Send alert
        self.send_security_alert('IP Address Blocked', {
            'ip_address': ip_address,
            'reason': reason
        })
    
    def unblock_ip(self, ip_address):
        """Unblock IP address"""
        if ip_address in self.blocked_ips:
            self.blocked_ips.remove(ip_address)
            self.save_security_data()
            
            self.log_event(
                'ip_unblocked',
                ip_address=ip_address,
                severity='INFO'
            )
    
    def is_ip_blocked(self, ip_address):
        """Check if IP is blocked"""
        return ip_address in self.blocked_ips
    
    def is_ip_suspicious(self, ip_address):
        """Check if IP is suspicious"""
        return ip_address in self.suspicious_ips
    
    def get_security_stats(self):
        """Get security statistics"""
        now = datetime.now()
        
        # Events in last 24 hours
        recent_events = [
            event for event in self.events
            if now - datetime.fromisoformat(event['timestamp']) < timedelta(hours=24)
        ]
        
        # Count by type
        event_counts = defaultdict(int)
        for event in recent_events:
            event_counts[event['type']] += 1
        
        # Count by severity
        severity_counts = defaultdict(int)
        for event in recent_events:
            severity_counts[event['severity']] += 1
        
        return {
            'total_events_24h': len(recent_events),
            'blocked_ips': len(self.blocked_ips),
            'suspicious_ips': len(self.suspicious_ips),
            'event_types': dict(event_counts),
            'severity_levels': dict(severity_counts),
            'top_suspicious_ips': list(self.suspicious_ips)[:10]
        }
    
    def get_recent_events(self, limit=100):
        """Get recent security events"""
        return list(self.events)[-limit:]
    
    def send_security_alert(self, subject, details):
        """Send security alert email"""
        try:
            # Email configuration (should be in environment variables)
            smtp_server = os.environ.get('SMTP_SERVER', 'localhost')
            smtp_port = int(os.environ.get('SMTP_PORT', '587'))
            smtp_username = os.environ.get('SMTP_USERNAME', '')
            smtp_password = os.environ.get('SMTP_PASSWORD', '')
            alert_email = os.environ.get('SECURITY_ALERT_EMAIL', '<EMAIL>')
            
            if not smtp_username or not alert_email:
                self.security_logger.warning("Email configuration missing, cannot send alert")
                return
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = smtp_username
            msg['To'] = alert_email
            msg['Subject'] = f"Security Alert: {subject}"
            
            # Email body
            body = f"""
Security Alert: {subject}

Details:
{json.dumps(details, indent=2)}

Timestamp: {datetime.now().isoformat()}

This is an automated security alert from the Legal Website Security Monitor.
Please investigate immediately.
            """
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(smtp_username, smtp_password)
            server.send_message(msg)
            server.quit()
            
            self.security_logger.info(f"Security alert sent: {subject}")
            
        except Exception as e:
            self.security_logger.error(f"Failed to send security alert: {e}")
    
    def cleanup_old_data(self):
        """Cleanup old security data"""
        now = datetime.now()
        
        # Clean IP attempts older than 24 hours
        for ip in list(self.ip_attempts.keys()):
            self.ip_attempts[ip] = [
                t for t in self.ip_attempts[ip]
                if now - t < timedelta(hours=24)
            ]
            if not self.ip_attempts[ip]:
                del self.ip_attempts[ip]
        
        # Clean user attempts older than 24 hours
        for user_id in list(self.user_attempts.keys()):
            self.user_attempts[user_id] = [
                t for t in self.user_attempts[user_id]
                if now - t < timedelta(hours=24)
            ]
            if not self.user_attempts[user_id]:
                del self.user_attempts[user_id]

# Global security monitor instance
security_monitor = SecurityMonitor()

# Utility functions
def log_security_event(event_type, user_id=None, ip_address=None, details=None, severity='INFO'):
    """Log security event"""
    security_monitor.log_event(event_type, user_id, ip_address, details, severity)

def is_ip_blocked(ip_address):
    """Check if IP is blocked"""
    return security_monitor.is_ip_blocked(ip_address)

def is_ip_suspicious(ip_address):
    """Check if IP is suspicious"""
    return security_monitor.is_ip_suspicious(ip_address)

def get_security_stats():
    """Get security statistics"""
    return security_monitor.get_security_stats()

def block_ip(ip_address, reason):
    """Block IP address"""
    security_monitor.block_ip(ip_address, reason)

def unblock_ip(ip_address):
    """Unblock IP address"""
    security_monitor.unblock_ip(ip_address)
