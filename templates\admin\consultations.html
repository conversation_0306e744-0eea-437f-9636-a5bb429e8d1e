{% extends "admin/base.html" %}

{% block title %}إدارة الاستشارات{% endblock %}
{% block page_title %}إدارة الاستشارات{% endblock %}

{% block content %}
<!-- Filter -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <div class="col-md-4">
                <label for="status" class="form-label">حالة الاستشارة</label>
                <select class="form-select" id="status" name="status">
                    <option value="">جميع الاستشارات</option>
                    <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>في الانتظار</option>
                    <option value="in_progress" {% if current_status == 'in_progress' %}selected{% endif %}>قيد المراجعة</option>
                    <option value="completed" {% if current_status == 'completed' %}selected{% endif %}>مكتملة</option>
                    <option value="cancelled" {% if current_status == 'cancelled' %}selected{% endif %}>ملغية</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="bi bi-funnel me-1"></i>فلترة
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Consultations Table -->
<div class="card">
    <div class="card-header bg-white">
        <h5 class="mb-0">
            <i class="bi bi-chat-dots me-2 text-primary-custom"></i>
            قائمة الاستشارات ({{ consultations|length }})
        </h5>
    </div>
    <div class="card-body p-0">
        {% if consultations %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="bg-light">
                    <tr>
                        <th class="px-4 py-3">العميل</th>
                        <th class="px-4 py-3">الموضوع</th>
                        <th class="px-4 py-3">المجال القانوني</th>
                        <th class="px-4 py-3">الحالة</th>
                        <th class="px-4 py-3">التاريخ</th>
                        <th class="px-4 py-3">الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for consultation in consultations %}
                    <tr>
                        <td class="px-4 py-3">
                            <div>
                                <h6 class="mb-1 fw-bold">{{ consultation.full_name }}</h6>
                                <small class="text-muted">{{ consultation.email }}</small>
                                {% if consultation.phone %}
                                <br><small class="text-muted">{{ consultation.phone }}</small>
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-4 py-3">
                            <div>
                                <h6 class="mb-1">{{ consultation.subject }}</h6>
                                <small class="text-muted">{{ consultation.message[:80] }}{% if consultation.message|length > 80 %}...{% endif %}</small>
                            </div>
                        </td>
                        <td class="px-4 py-3">
                            {% if consultation.legal_field %}
                            <span class="badge bg-info">{{ consultation.legal_field }}</span>
                            {% else %}
                            <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td class="px-4 py-3">
                            {% set status = consultation.status or 'pending' %}
                            {% if status == 'pending' %}
                                <span class="badge bg-warning">في الانتظار</span>
                            {% elif status == 'in_progress' %}
                                <span class="badge bg-info">قيد المراجعة</span>
                            {% elif status == 'completed' %}
                                <span class="badge bg-success">مكتملة</span>
                            {% elif status == 'cancelled' %}
                                <span class="badge bg-danger">ملغية</span>
                            {% endif %}
                        </td>
                        <td class="px-4 py-3">
                            <small class="text-muted">{{ consultation.created_at[:10] }}</small>
                        </td>
                        <td class="px-4 py-3">
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('admin_consultation_detail', consultation_id=consultation.id) }}"
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <div class="dropdown">
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                            data-bs-toggle="dropdown" title="تغيير الحالة">
                                        <i class="bi bi-gear"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="updateStatus({{ consultation.id }}, 'pending')">
                                            <i class="bi bi-clock text-warning me-2"></i>في الانتظار
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateStatus({{ consultation.id }}, 'in_progress')">
                                            <i class="bi bi-arrow-clockwise text-info me-2"></i>قيد المراجعة
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateStatus({{ consultation.id }}, 'completed')">
                                            <i class="bi bi-check-circle text-success me-2"></i>مكتملة
                                        </a></li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li><a class="dropdown-item" href="#" onclick="updateStatus({{ consultation.id }}, 'cancelled')">
                                            <i class="bi bi-x-circle text-danger me-2"></i>ملغية
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="bi bi-chat-dots display-1 text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد استشارات</h4>
            {% if current_status %}
            <p class="text-muted">لا توجد استشارات بالحالة المحددة</p>
            <a href="{{ url_for('admin_consultations') }}" class="btn btn-outline-primary">
                <i class="bi bi-arrow-clockwise me-1"></i>
                عرض جميع الاستشارات
            </a>
            {% else %}
            <p class="text-muted">لم يتم استلام أي استشارات بعد</p>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Quick Stats -->
{% if consultations %}
<div class="row mt-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card warning">
            <div class="card-body text-center">
                <i class="bi bi-clock display-4 mb-2"></i>
                <h4 class="fw-bold">
                    {% set pending_count = 0 %}
                    {% for consultation in consultations %}
                        {% if consultation.status == 'pending' or not consultation.status %}
                            {% set pending_count = pending_count + 1 %}
                        {% endif %}
                    {% endfor %}
                    {{ pending_count }}
                </h4>
                <small>في الانتظار</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="bi bi-arrow-clockwise display-4 mb-2"></i>
                <h4 class="fw-bold">
                    {% set in_progress_count = 0 %}
                    {% for consultation in consultations %}
                        {% if consultation.status == 'in_progress' %}
                            {% set in_progress_count = in_progress_count + 1 %}
                        {% endif %}
                    {% endfor %}
                    {{ in_progress_count }}
                </h4>
                <small>قيد المراجعة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card success">
            <div class="card-body text-center">
                <i class="bi bi-check-circle display-4 mb-2"></i>
                <h4 class="fw-bold">
                    {% set completed_count = 0 %}
                    {% for consultation in consultations %}
                        {% if consultation.status == 'completed' %}
                            {% set completed_count = completed_count + 1 %}
                        {% endif %}
                    {% endfor %}
                    {{ completed_count }}
                </h4>
                <small>مكتملة</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card stats-card gold">
            <div class="card-body text-center">
                <i class="bi bi-chat-dots display-4 mb-2"></i>
                <h4 class="fw-bold">{{ consultations|length }}</h4>
                <small>إجمالي</small>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit form on status change
document.getElementById('status').addEventListener('change', function() {
    this.form.submit();
});

function updateStatus(consultationId, newStatus) {
    if (confirm('هل تريد تغيير حالة هذه الاستشارة؟')) {
        // Here you would make an AJAX call to update the status
        // For now, we'll just reload the page
        alert('ميزة تحديث الحالة قيد التطوير');
    }
}
</script>
{% endblock %}
