"""
Advanced Content Management System
AI-powered content optimization and management
"""

import json
import os
import re
from datetime import datetime, timedelta
from collections import defaultdict
import logging
from textstat import flesch_reading_ease, flesch_kincaid_grade
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize, sent_tokenize
from nltk.stem import SnowballStemmer

# Download required NLTK data
try:
    nltk.download('punkt', quiet=True)
    nltk.download('stopwords', quiet=True)
except:
    pass

class ContentManagementSystem:
    """Advanced CMS with AI-powered content optimization"""
    
    def __init__(self):
        self.content_cache = {}
        self.seo_cache = {}
        self.analytics_data = defaultdict(list)
        
        # Content optimization settings
        self.optimization_settings = {
            'target_reading_level': 8,  # Grade level
            'min_word_count': 300,
            'max_word_count': 2000,
            'target_keyword_density': 2.5,  # Percentage
            'min_internal_links': 3,
            'min_external_links': 1,
            'required_headings': ['h1', 'h2'],
            'image_alt_text_required': True,
            'meta_description_length': (120, 160),
            'title_length': (30, 60)
        }
        
        # Arabic language support
        self.arabic_stopwords = {
            'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك',
            'التي', 'الذي', 'التي', 'اللذان', 'اللتان', 'اللذين', 'اللتين',
            'كان', 'كانت', 'يكون', 'تكون', 'أن', 'إن', 'لكن', 'لكن', 'غير',
            'سوف', 'قد', 'لقد', 'منذ', 'حتى', 'بعد', 'قبل', 'أثناء', 'خلال'
        }
        
        # Setup logging
        self.setup_logging()
    
    def setup_logging(self):
        """Setup CMS logging"""
        log_dir = 'logs'
        os.makedirs(log_dir, exist_ok=True)
        
        self.logger = logging.getLogger('content_management')
        self.logger.setLevel(logging.INFO)
        
        # File handler
        file_handler = logging.FileHandler(f'{log_dir}/content_management.log')
        file_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def analyze_content(self, content, language='ar'):
        """Comprehensive content analysis"""
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'language': language,
            'basic_stats': self.get_basic_stats(content),
            'readability': self.analyze_readability(content, language),
            'seo_analysis': self.analyze_seo(content),
            'structure_analysis': self.analyze_structure(content),
            'keyword_analysis': self.analyze_keywords(content, language),
            'quality_score': 0,
            'recommendations': []
        }
        
        # Calculate overall quality score
        analysis['quality_score'] = self.calculate_quality_score(analysis)
        
        # Generate recommendations
        analysis['recommendations'] = self.generate_recommendations(analysis)
        
        return analysis
    
    def get_basic_stats(self, content):
        """Get basic content statistics"""
        # Remove HTML tags for accurate counting
        clean_content = re.sub(r'<[^>]+>', '', content)
        
        words = word_tokenize(clean_content)
        sentences = sent_tokenize(clean_content)
        paragraphs = content.split('\n\n')
        
        return {
            'word_count': len(words),
            'sentence_count': len(sentences),
            'paragraph_count': len([p for p in paragraphs if p.strip()]),
            'character_count': len(clean_content),
            'character_count_no_spaces': len(clean_content.replace(' ', '')),
            'average_words_per_sentence': len(words) / len(sentences) if sentences else 0,
            'average_sentences_per_paragraph': len(sentences) / len(paragraphs) if paragraphs else 0
        }
    
    def analyze_readability(self, content, language='ar'):
        """Analyze content readability"""
        clean_content = re.sub(r'<[^>]+>', '', content)
        
        if language == 'en':
            try:
                flesch_score = flesch_reading_ease(clean_content)
                grade_level = flesch_kincaid_grade(clean_content)
                
                return {
                    'flesch_reading_ease': flesch_score,
                    'grade_level': grade_level,
                    'readability_level': self.get_readability_level(flesch_score)
                }
            except:
                return {
                    'flesch_reading_ease': 0,
                    'grade_level': 0,
                    'readability_level': 'unknown'
                }
        else:
            # Arabic readability analysis (simplified)
            words = word_tokenize(clean_content)
            sentences = sent_tokenize(clean_content)
            
            avg_words_per_sentence = len(words) / len(sentences) if sentences else 0
            
            # Simple Arabic readability score
            if avg_words_per_sentence < 15:
                readability_level = 'easy'
                score = 80
            elif avg_words_per_sentence < 25:
                readability_level = 'medium'
                score = 60
            else:
                readability_level = 'difficult'
                score = 40
            
            return {
                'arabic_readability_score': score,
                'average_words_per_sentence': avg_words_per_sentence,
                'readability_level': readability_level
            }
    
    def get_readability_level(self, flesch_score):
        """Convert Flesch score to readability level"""
        if flesch_score >= 90:
            return 'very_easy'
        elif flesch_score >= 80:
            return 'easy'
        elif flesch_score >= 70:
            return 'fairly_easy'
        elif flesch_score >= 60:
            return 'standard'
        elif flesch_score >= 50:
            return 'fairly_difficult'
        elif flesch_score >= 30:
            return 'difficult'
        else:
            return 'very_difficult'
    
    def analyze_seo(self, content):
        """Analyze SEO aspects of content"""
        seo_analysis = {
            'title_analysis': self.analyze_title(content),
            'meta_description_analysis': self.analyze_meta_description(content),
            'heading_analysis': self.analyze_headings(content),
            'link_analysis': self.analyze_links(content),
            'image_analysis': self.analyze_images(content),
            'keyword_density': self.calculate_keyword_density(content)
        }
        
        return seo_analysis
    
    def analyze_title(self, content):
        """Analyze page title"""
        title_match = re.search(r'<title[^>]*>([^<]+)</title>', content, re.IGNORECASE)
        
        if not title_match:
            return {
                'exists': False,
                'length': 0,
                'recommendations': ['Add a title tag']
            }
        
        title = title_match.group(1).strip()
        length = len(title)
        
        recommendations = []
        if length < self.optimization_settings['title_length'][0]:
            recommendations.append(f'Title too short (minimum {self.optimization_settings["title_length"][0]} characters)')
        elif length > self.optimization_settings['title_length'][1]:
            recommendations.append(f'Title too long (maximum {self.optimization_settings["title_length"][1]} characters)')
        
        return {
            'exists': True,
            'title': title,
            'length': length,
            'recommendations': recommendations
        }
    
    def analyze_meta_description(self, content):
        """Analyze meta description"""
        desc_match = re.search(
            r'<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']+)["\']',
            content, re.IGNORECASE
        )
        
        if not desc_match:
            return {
                'exists': False,
                'length': 0,
                'recommendations': ['Add a meta description']
            }
        
        description = desc_match.group(1).strip()
        length = len(description)
        
        recommendations = []
        min_length, max_length = self.optimization_settings['meta_description_length']
        
        if length < min_length:
            recommendations.append(f'Meta description too short (minimum {min_length} characters)')
        elif length > max_length:
            recommendations.append(f'Meta description too long (maximum {max_length} characters)')
        
        return {
            'exists': True,
            'description': description,
            'length': length,
            'recommendations': recommendations
        }
    
    def analyze_headings(self, content):
        """Analyze heading structure"""
        headings = {
            'h1': re.findall(r'<h1[^>]*>([^<]+)</h1>', content, re.IGNORECASE),
            'h2': re.findall(r'<h2[^>]*>([^<]+)</h2>', content, re.IGNORECASE),
            'h3': re.findall(r'<h3[^>]*>([^<]+)</h3>', content, re.IGNORECASE),
            'h4': re.findall(r'<h4[^>]*>([^<]+)</h4>', content, re.IGNORECASE),
            'h5': re.findall(r'<h5[^>]*>([^<]+)</h5>', content, re.IGNORECASE),
            'h6': re.findall(r'<h6[^>]*>([^<]+)</h6>', content, re.IGNORECASE)
        }
        
        recommendations = []
        
        if len(headings['h1']) == 0:
            recommendations.append('Add an H1 heading')
        elif len(headings['h1']) > 1:
            recommendations.append('Use only one H1 heading per page')
        
        if len(headings['h2']) < 2:
            recommendations.append('Add more H2 headings to structure content')
        
        return {
            'headings': headings,
            'structure_score': self.calculate_heading_structure_score(headings),
            'recommendations': recommendations
        }
    
    def calculate_heading_structure_score(self, headings):
        """Calculate heading structure quality score"""
        score = 0
        
        # H1 scoring
        if len(headings['h1']) == 1:
            score += 25
        elif len(headings['h1']) == 0:
            score -= 10
        
        # H2 scoring
        if 2 <= len(headings['h2']) <= 6:
            score += 25
        elif len(headings['h2']) > 6:
            score += 15
        
        # Hierarchy scoring
        if headings['h1'] and headings['h2']:
            score += 20
        
        # Content distribution
        if headings['h3']:
            score += 15
        
        return max(0, min(100, score))
    
    def analyze_links(self, content):
        """Analyze internal and external links"""
        all_links = re.findall(r'<a[^>]*href=["\']([^"\']+)["\'][^>]*>([^<]*)</a>', content, re.IGNORECASE)
        
        internal_links = []
        external_links = []
        
        for href, text in all_links:
            if href.startswith('http'):
                if 'localhost' in href or 'legal-site.com' in href:
                    internal_links.append((href, text))
                else:
                    external_links.append((href, text))
            elif href.startswith('/') or not href.startswith('#'):
                internal_links.append((href, text))
        
        recommendations = []
        
        if len(internal_links) < self.optimization_settings['min_internal_links']:
            recommendations.append(f'Add more internal links (minimum {self.optimization_settings["min_internal_links"]})')
        
        if len(external_links) < self.optimization_settings['min_external_links']:
            recommendations.append(f'Add authoritative external links (minimum {self.optimization_settings["min_external_links"]})')
        
        return {
            'internal_links': internal_links,
            'external_links': external_links,
            'total_links': len(all_links),
            'recommendations': recommendations
        }
    
    def analyze_images(self, content):
        """Analyze images and alt text"""
        images = re.findall(r'<img[^>]*>', content, re.IGNORECASE)
        
        images_with_alt = 0
        images_without_alt = 0
        
        for img in images:
            if 'alt=' in img.lower():
                images_with_alt += 1
            else:
                images_without_alt += 1
        
        recommendations = []
        
        if images_without_alt > 0:
            recommendations.append(f'Add alt text to {images_without_alt} images')
        
        return {
            'total_images': len(images),
            'images_with_alt': images_with_alt,
            'images_without_alt': images_without_alt,
            'alt_text_percentage': (images_with_alt / len(images) * 100) if images else 100,
            'recommendations': recommendations
        }
    
    def calculate_keyword_density(self, content, target_keyword=None):
        """Calculate keyword density"""
        clean_content = re.sub(r'<[^>]+>', '', content).lower()
        words = word_tokenize(clean_content)
        
        # Remove stopwords
        filtered_words = [word for word in words if word not in self.arabic_stopwords and len(word) > 2]
        
        # Count word frequencies
        word_freq = defaultdict(int)
        for word in filtered_words:
            word_freq[word] += 1
        
        total_words = len(filtered_words)
        
        # Calculate densities
        keyword_densities = {}
        for word, count in word_freq.items():
            density = (count / total_words) * 100
            keyword_densities[word] = {
                'count': count,
                'density': density
            }
        
        # Sort by density
        sorted_keywords = sorted(keyword_densities.items(), key=lambda x: x[1]['density'], reverse=True)
        
        return {
            'top_keywords': sorted_keywords[:10],
            'total_words': total_words,
            'unique_words': len(word_freq),
            'keyword_diversity': len(word_freq) / total_words if total_words > 0 else 0
        }
    
    def analyze_structure(self, content):
        """Analyze content structure"""
        # Count different elements
        paragraphs = len(re.findall(r'<p[^>]*>', content))
        lists = len(re.findall(r'<(ul|ol)[^>]*>', content))
        blockquotes = len(re.findall(r'<blockquote[^>]*>', content))
        tables = len(re.findall(r'<table[^>]*>', content))
        
        structure_score = 0
        
        # Scoring based on structure elements
        if paragraphs >= 3:
            structure_score += 25
        if lists >= 1:
            structure_score += 20
        if blockquotes >= 1:
            structure_score += 15
        if tables >= 1:
            structure_score += 10
        
        return {
            'paragraphs': paragraphs,
            'lists': lists,
            'blockquotes': blockquotes,
            'tables': tables,
            'structure_score': structure_score,
            'has_good_structure': structure_score >= 50
        }
    
    def analyze_keywords(self, content, language='ar'):
        """Advanced keyword analysis"""
        clean_content = re.sub(r'<[^>]+>', '', content).lower()
        
        if language == 'ar':
            # Arabic text processing
            words = re.findall(r'[\u0600-\u06FF]+', clean_content)
            filtered_words = [word for word in words if word not in self.arabic_stopwords and len(word) > 2]
        else:
            words = word_tokenize(clean_content)
            try:
                stop_words = set(stopwords.words('english'))
                filtered_words = [word for word in words if word.lower() not in stop_words and len(word) > 2]
            except:
                filtered_words = [word for word in words if len(word) > 2]
        
        # Keyword frequency analysis
        word_freq = defaultdict(int)
        for word in filtered_words:
            word_freq[word] += 1
        
        # Find keyword phrases (2-3 words)
        phrases = []
        for i in range(len(filtered_words) - 1):
            phrase = ' '.join(filtered_words[i:i+2])
            phrases.append(phrase)
        
        phrase_freq = defaultdict(int)
        for phrase in phrases:
            phrase_freq[phrase] += 1
        
        return {
            'top_single_keywords': sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10],
            'top_keyword_phrases': sorted(phrase_freq.items(), key=lambda x: x[1], reverse=True)[:10],
            'total_keywords': len(filtered_words),
            'unique_keywords': len(word_freq),
            'keyword_variety': len(word_freq) / len(filtered_words) if filtered_words else 0
        }
    
    def calculate_quality_score(self, analysis):
        """Calculate overall content quality score"""
        score = 0
        max_score = 100
        
        # Basic stats scoring (20 points)
        word_count = analysis['basic_stats']['word_count']
        if self.optimization_settings['min_word_count'] <= word_count <= self.optimization_settings['max_word_count']:
            score += 20
        elif word_count < self.optimization_settings['min_word_count']:
            score += (word_count / self.optimization_settings['min_word_count']) * 20
        else:
            score += 15  # Penalty for too long content
        
        # SEO scoring (30 points)
        seo = analysis['seo_analysis']
        if seo['title_analysis']['exists'] and not seo['title_analysis']['recommendations']:
            score += 10
        if seo['meta_description_analysis']['exists'] and not seo['meta_description_analysis']['recommendations']:
            score += 10
        if seo['heading_analysis']['structure_score'] >= 70:
            score += 10
        
        # Structure scoring (20 points)
        if analysis['structure_analysis']['has_good_structure']:
            score += 20
        else:
            score += analysis['structure_analysis']['structure_score'] / 5
        
        # Readability scoring (15 points)
        if analysis['language'] == 'ar':
            readability_score = analysis['readability']['arabic_readability_score']
            score += (readability_score / 100) * 15
        else:
            flesch_score = analysis['readability']['flesch_reading_ease']
            if 60 <= flesch_score <= 80:  # Optimal range
                score += 15
            else:
                score += max(0, (flesch_score / 100) * 15)
        
        # Image and link scoring (15 points)
        if analysis['seo_analysis']['image_analysis']['alt_text_percentage'] >= 90:
            score += 8
        if len(analysis['seo_analysis']['link_analysis']['internal_links']) >= 3:
            score += 7
        
        return min(max_score, score)
    
    def generate_recommendations(self, analysis):
        """Generate content improvement recommendations"""
        recommendations = []
        
        # Collect all recommendations from sub-analyses
        for section in ['seo_analysis', 'structure_analysis']:
            if section in analysis:
                section_data = analysis[section]
                if isinstance(section_data, dict):
                    for key, value in section_data.items():
                        if isinstance(value, dict) and 'recommendations' in value:
                            recommendations.extend(value['recommendations'])
        
        # Add quality-based recommendations
        quality_score = analysis['quality_score']
        
        if quality_score < 50:
            recommendations.append('Content needs significant improvement')
        elif quality_score < 70:
            recommendations.append('Content is good but could be optimized further')
        elif quality_score >= 90:
            recommendations.append('Excellent content quality!')
        
        # Word count recommendations
        word_count = analysis['basic_stats']['word_count']
        if word_count < self.optimization_settings['min_word_count']:
            recommendations.append(f'Increase content length to at least {self.optimization_settings["min_word_count"]} words')
        elif word_count > self.optimization_settings['max_word_count']:
            recommendations.append(f'Consider breaking content into multiple pages or reducing to under {self.optimization_settings["max_word_count"]} words')
        
        return list(set(recommendations))  # Remove duplicates
    
    def optimize_content_for_seo(self, content, target_keywords=None):
        """Automatically optimize content for SEO"""
        optimized_content = content
        
        # Add missing meta tags
        if '<meta name="description"' not in content:
            # Extract first paragraph as description
            first_para = re.search(r'<p[^>]*>([^<]+)</p>', content)
            if first_para:
                description = first_para.group(1)[:150] + '...'
                meta_tag = f'<meta name="description" content="{description}">'
                optimized_content = optimized_content.replace('<head>', f'<head>\n    {meta_tag}')
        
        # Add missing alt text to images
        img_pattern = r'<img([^>]*?)(?<!alt=")(?<!alt=\')>'
        def add_alt_text(match):
            img_attrs = match.group(1)
            return f'<img{img_attrs} alt="صورة توضيحية">'
        
        optimized_content = re.sub(img_pattern, add_alt_text, optimized_content)
        
        return optimized_content
    
    def generate_content_report(self, content, title="Content Analysis Report"):
        """Generate comprehensive content analysis report"""
        analysis = self.analyze_content(content)
        
        report = {
            'title': title,
            'timestamp': datetime.now().isoformat(),
            'analysis': analysis,
            'summary': {
                'quality_score': analysis['quality_score'],
                'grade': self.get_quality_grade(analysis['quality_score']),
                'word_count': analysis['basic_stats']['word_count'],
                'readability_level': analysis['readability'].get('readability_level', 'unknown'),
                'seo_issues': len([r for section in analysis['seo_analysis'].values() 
                                 if isinstance(section, dict) and 'recommendations' in section 
                                 for r in section['recommendations']]),
                'total_recommendations': len(analysis['recommendations'])
            }
        }
        
        return report
    
    def get_quality_grade(self, score):
        """Convert quality score to letter grade"""
        if score >= 90:
            return 'A+'
        elif score >= 80:
            return 'A'
        elif score >= 70:
            return 'B'
        elif score >= 60:
            return 'C'
        elif score >= 50:
            return 'D'
        else:
            return 'F'

# Global CMS instance
cms = ContentManagementSystem()

if __name__ == '__main__':
    # Test with sample content
    sample_content = """
    <html>
    <head>
        <title>مقدمة في القانون المغربي</title>
        <meta name="description" content="دليل شامل للنظام القانوني المغربي">
    </head>
    <body>
        <h1>مقدمة في القانون المغربي</h1>
        <p>يعتبر النظام القانوني المغربي نظاماً متميزاً يجمع بين التراث الإسلامي والقانون الحديث.</p>
        <h2>مصادر القانون المغربي</h2>
        <p>يستند القانون المغربي إلى عدة مصادر أساسية.</p>
        <img src="law.jpg" alt="القانون المغربي">
        <a href="/articles">المقالات</a>
    </body>
    </html>
    """
    
    report = cms.generate_content_report(sample_content)
    print(json.dumps(report, ensure_ascii=False, indent=2))
