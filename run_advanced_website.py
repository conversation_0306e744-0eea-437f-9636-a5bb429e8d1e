#!/usr/bin/env python3
"""
Advanced Website Launcher
Starts all systems with comprehensive monitoring and optimization
"""

import os
import sys
import time
import threading
import signal
import logging
from datetime import datetime

# Import all advanced systems
try:
    from simple_secure_app import app as secure_app
    SECURE_APP_AVAILABLE = True
except ImportError:
    SECURE_APP_AVAILABLE = False
    print("⚠️  Secure app not available, using basic mode")

try:
    from advanced_monitoring import website_monitor
    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False
    print("⚠️  Advanced monitoring not available")

try:
    from backup_system import backup_system
    BACKUP_AVAILABLE = True
except ImportError:
    BACKUP_AVAILABLE = False
    print("⚠️  Backup system not available")

try:
    from performance_optimizer import performance_optimizer
    PERFORMANCE_AVAILABLE = True
except ImportError:
    PERFORMANCE_AVAILABLE = False
    print("⚠️  Performance optimizer not available")

class AdvancedWebsiteLauncher:
    """Advanced website launcher with all systems"""
    
    def __init__(self):
        self.running = False
        self.threads = []
        
        # Setup logging
        self.setup_logging()
        
        # System status
        self.system_status = {
            'website': False,
            'monitoring': False,
            'backup': False,
            'performance': False,
            'analytics': False
        }
    
    def setup_logging(self):
        """Setup launcher logging"""
        log_dir = 'logs'
        os.makedirs(log_dir, exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f'{log_dir}/launcher.log'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger('advanced_launcher')
    
    def start_website(self):
        """Start the secure website"""
        if not SECURE_APP_AVAILABLE:
            self.logger.error("Secure app not available")
            return False
        
        try:
            def run_website():
                self.logger.info("Starting secure website on port 5002...")
                secure_app.run(debug=False, host='127.0.0.1', port=5002, use_reloader=False)
            
            website_thread = threading.Thread(target=run_website)
            website_thread.daemon = True
            website_thread.start()
            self.threads.append(website_thread)
            
            # Wait a moment for startup
            time.sleep(2)
            
            # Test if website is running
            import requests
            try:
                response = requests.get('http://127.0.0.1:5002', timeout=5)
                if response.status_code == 200:
                    self.system_status['website'] = True
                    self.logger.info("✅ Website started successfully")
                    return True
            except:
                pass
            
            self.logger.error("❌ Website failed to start")
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to start website: {e}")
            return False
    
    def start_monitoring(self):
        """Start advanced monitoring"""
        if not MONITORING_AVAILABLE:
            self.logger.warning("Advanced monitoring not available")
            return False
        
        try:
            def run_monitoring():
                self.logger.info("Starting advanced monitoring...")
                website_monitor.start_monitoring(interval=30)
                
                while self.running:
                    time.sleep(60)
                    report = website_monitor.get_monitoring_report()
                    self.logger.info(f"Monitoring: Uptime {report.get('uptime_percentage', 0):.1f}%")
            
            monitoring_thread = threading.Thread(target=run_monitoring)
            monitoring_thread.daemon = True
            monitoring_thread.start()
            self.threads.append(monitoring_thread)
            
            self.system_status['monitoring'] = True
            self.logger.info("✅ Advanced monitoring started")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start monitoring: {e}")
            return False
    
    def start_backup_system(self):
        """Start backup system"""
        if not BACKUP_AVAILABLE:
            self.logger.warning("Backup system not available")
            return False
        
        try:
            def run_backup_system():
                self.logger.info("Starting backup system...")
                backup_system.start_scheduler()
                
                # Create initial backup
                backup_system.create_full_backup()
                
                while self.running:
                    time.sleep(3600)  # Check every hour
                    status = backup_system.get_backup_status()
                    self.logger.info(f"Backup: {status.get('total_backups', 0)} backups, "
                                   f"{status.get('total_size', 0) / (1024*1024):.1f} MB")
            
            backup_thread = threading.Thread(target=run_backup_system)
            backup_thread.daemon = True
            backup_thread.start()
            self.threads.append(backup_thread)
            
            self.system_status['backup'] = True
            self.logger.info("✅ Backup system started")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start backup system: {e}")
            return False
    
    def start_performance_optimizer(self):
        """Start performance optimizer"""
        if not PERFORMANCE_AVAILABLE:
            self.logger.warning("Performance optimizer not available")
            return False
        
        try:
            def run_performance_optimizer():
                self.logger.info("Starting performance optimizer...")
                
                while self.running:
                    time.sleep(300)  # Check every 5 minutes
                    
                    # Get performance stats
                    stats = performance_optimizer.get_performance_stats()
                    
                    # Log performance summary
                    cpu_usage = stats.get('system', {}).get('cpu_percent', 0)
                    memory_usage = stats.get('system', {}).get('memory_percent', 0)
                    cache_hit_rate = stats.get('cache', {}).get('hit_rate', 0)
                    
                    self.logger.info(f"Performance: CPU {cpu_usage:.1f}%, "
                                   f"Memory {memory_usage:.1f}%, "
                                   f"Cache {cache_hit_rate:.1f}%")
                    
                    # Auto-optimize if needed
                    if memory_usage > 80:
                        performance_optimizer.optimize_memory()
            
            performance_thread = threading.Thread(target=run_performance_optimizer)
            performance_thread.daemon = True
            performance_thread.start()
            self.threads.append(performance_thread)
            
            self.system_status['performance'] = True
            self.logger.info("✅ Performance optimizer started")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start performance optimizer: {e}")
            return False
    
    def start_analytics(self):
        """Start analytics system"""
        try:
            # Simple analytics logging
            def run_analytics():
                self.logger.info("Starting analytics system...")
                
                while self.running:
                    time.sleep(600)  # Log every 10 minutes
                    
                    # Simple analytics data
                    analytics_data = {
                        'timestamp': datetime.now().isoformat(),
                        'systems_running': sum(self.system_status.values()),
                        'uptime_minutes': int(time.time() - self.start_time) // 60
                    }
                    
                    # Save analytics data
                    analytics_file = 'analytics_data/runtime_analytics.json'
                    os.makedirs('analytics_data', exist_ok=True)
                    
                    import json
                    with open(analytics_file, 'a') as f:
                        f.write(json.dumps(analytics_data) + '\n')
                    
                    self.logger.info(f"Analytics: {analytics_data['systems_running']}/5 systems running, "
                                   f"Uptime: {analytics_data['uptime_minutes']} minutes")
            
            analytics_thread = threading.Thread(target=run_analytics)
            analytics_thread.daemon = True
            analytics_thread.start()
            self.threads.append(analytics_thread)
            
            self.system_status['analytics'] = True
            self.logger.info("✅ Analytics system started")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start analytics: {e}")
            return False
    
    def display_status(self):
        """Display system status"""
        print("\n" + "="*60)
        print("🌟 ADVANCED LEGAL WEBSITE - SYSTEM STATUS")
        print("="*60)
        
        status_icons = {True: "✅", False: "❌"}
        
        print(f"{status_icons[self.system_status['website']]} Website (Port 5002)")
        print(f"{status_icons[self.system_status['monitoring']]} Advanced Monitoring")
        print(f"{status_icons[self.system_status['backup']]} Backup System")
        print(f"{status_icons[self.system_status['performance']]} Performance Optimizer")
        print(f"{status_icons[self.system_status['analytics']]} Analytics System")
        
        running_systems = sum(self.system_status.values())
        print(f"\n📊 Systems Running: {running_systems}/5")
        
        if running_systems == 5:
            print("🎉 All systems operational!")
        elif running_systems >= 3:
            print("⚠️  Most systems operational")
        else:
            print("🚨 Multiple system failures")
        
        print("\n🌐 Website URL: http://127.0.0.1:5002")
        print("📁 Logs Directory: logs/")
        print("💾 Backups Directory: backups/")
        print("📊 Analytics Directory: analytics_data/")
        print("\n" + "="*60)
    
    def start_all_systems(self):
        """Start all systems"""
        self.running = True
        self.start_time = time.time()
        
        print("🚀 Starting Advanced Legal Website...")
        print("Please wait while all systems initialize...\n")
        
        # Start systems in order
        self.start_website()
        time.sleep(1)
        
        self.start_monitoring()
        time.sleep(1)
        
        self.start_backup_system()
        time.sleep(1)
        
        self.start_performance_optimizer()
        time.sleep(1)
        
        self.start_analytics()
        time.sleep(2)
        
        # Display final status
        self.display_status()
        
        return sum(self.system_status.values()) >= 1  # At least one system running
    
    def stop_all_systems(self):
        """Stop all systems"""
        self.logger.info("Stopping all systems...")
        self.running = False
        
        # Stop monitoring
        if MONITORING_AVAILABLE:
            try:
                website_monitor.stop_monitoring()
            except:
                pass
        
        print("\n🛑 All systems stopped")
        print("Thank you for using Advanced Legal Website!")
    
    def run(self):
        """Main run method"""
        try:
            if self.start_all_systems():
                print("\n✨ Advanced Legal Website is running!")
                print("Press Ctrl+C to stop all systems\n")
                
                # Keep running
                while self.running:
                    time.sleep(10)
                    
                    # Check if website is still running
                    try:
                        import requests
                        response = requests.get('http://127.0.0.1:5002', timeout=2)
                        if response.status_code != 200:
                            self.logger.warning("Website health check failed")
                    except:
                        self.logger.warning("Website appears to be down")
            else:
                print("❌ Failed to start systems")
                return 1
                
        except KeyboardInterrupt:
            print("\n\n🛑 Shutdown requested...")
            self.stop_all_systems()
            return 0
        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
            self.stop_all_systems()
            return 1

def main():
    """Main function"""
    launcher = AdvancedWebsiteLauncher()
    return launcher.run()

if __name__ == '__main__':
    sys.exit(main())
