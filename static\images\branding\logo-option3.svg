<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 210 65" width="210" height="65">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8860B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="navyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="2" dy="2" stdDeviation="2" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- Outer Circle -->
  <circle cx="32.5" cy="32.5" r="28" fill="url(#navyGradient)" filter="url(#shadow)"/>
  <circle cx="32.5" cy="32.5" r="24" fill="none" stroke="url(#goldGradient)" stroke-width="2"/>
  
  <!-- Inner Symbol - Stylized Gavel -->
  <g transform="translate(32.5, 32.5)">
    <!-- Gavel Handle -->
    <rect x="-2" y="-15" width="4" height="20" fill="url(#goldGradient)" rx="2"/>
    
    <!-- Gavel Head -->
    <rect x="-8" y="-18" width="16" height="6" fill="url(#goldGradient)" rx="3"/>
    
    <!-- Base -->
    <ellipse cx="0" cy="8" rx="12" ry="3" fill="url(#goldGradient)" opacity="0.8"/>
    
    <!-- Decorative Stars -->
    <polygon points="0,-8 1,-6 3,-6 1,-4 2,-2 0,-3 -2,-2 -1,-4 -3,-6 -1,-6" 
             fill="url(#goldGradient)" opacity="0.6"/>
  </g>
  
  <!-- Arabic Text -->
  <text x="75" y="28" font-family="Arial, sans-serif" font-size="22" font-weight="bold" fill="url(#navyGradient)">
    قانوني
  </text>
  <text x="75" y="45" font-family="Arial, sans-serif" font-size="12" fill="url(#navyGradient)" opacity="0.8">
    موقع قانوني متخصص
  </text>
  <text x="75" y="58" font-family="Arial, sans-serif" font-size="9" fill="url(#navyGradient)" opacity="0.6">
    Legal Expert Platform
  </text>
  
  <!-- Decorative Elements -->
  <circle cx="190" cy="20" r="2" fill="url(#goldGradient)" opacity="0.4"/>
  <circle cx="195" cy="35" r="1.5" fill="url(#goldGradient)" opacity="0.3"/>
  <circle cx="185" cy="50" r="1" fill="url(#goldGradient)" opacity="0.2"/>
</svg>
