from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, make_response
from flask_wtf.csrf import CSRFProtect, validate_csrf
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
from flask_talisman import Talisman
from datetime import datetime, timedelta
import json
import os
import re
import secrets
import bcrypt
import bleach
import logging
from werkzeug.utils import secure_filename
from markupsafe import Markup

# Create Flask app with security enhancements
app = Flask(__name__)

# Security Configuration
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or secrets.token_hex(32)
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)  # Session timeout

# Security Headers with Talisman
csp = {
    'default-src': "'self'",
    'script-src': "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
    'style-src': "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com",
    'img-src': "'self' data: https:",
    'font-src': "'self' https://fonts.gstatic.com https://cdn.jsdelivr.net",
    'connect-src': "'self'",
    'frame-ancestors': "'none'",
    'base-uri': "'self'",
    'object-src': "'none'"
}

talisman = Talisman(
    app,
    force_https=False,  # Set to True in production
    strict_transport_security=True,
    content_security_policy=csp,
    content_security_policy_nonce_in=['script-src', 'style-src'],
    feature_policy={
        'geolocation': "'none'",
        'camera': "'none'",
        'microphone': "'none'"
    }
)

# CSRF Protection
csrf = CSRFProtect(app)

# Rate Limiting
limiter = Limiter(
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"],
    storage_uri="memory://"
)
limiter.init_app(app)

# Logging Configuration
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(name)s %(message)s',
    handlers=[
        logging.FileHandler('security.log'),
        logging.StreamHandler()
    ]
)
security_logger = logging.getLogger('security')

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Simple data storage
DATA_FILE = 'secure_enhanced_data.json'

def hash_password(password):
    """Hash password using bcrypt"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def check_password(password, hashed):
    """Check password against hash"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def sanitize_input(text):
    """Sanitize user input to prevent XSS"""
    if not text:
        return ""
    # Allow only safe HTML tags
    allowed_tags = ['p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                   'ul', 'ol', 'li', 'blockquote', 'a']
    allowed_attributes = {'a': ['href', 'title']}
    return bleach.clean(text, tags=allowed_tags, attributes=allowed_attributes, strip=True)

def log_security_event(event_type, user_id=None, details="", ip_address=None):
    """Log security events"""
    security_logger.warning(f"SECURITY EVENT: {event_type} | User: {user_id} | IP: {ip_address} | Details: {details}")

def load_data():
    """Load data from JSON file"""
    if os.path.exists(DATA_FILE):
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)

    # Initialize with secure default data
    default_password = hash_password('JA138985kala@!!')
    return {
        'users': [
            {
                'id': 1,
                'username': 'mcmedo36',
                'password_hash': default_password,  # Now hashed
                'email': '<EMAIL>',
                'is_admin': True,
                'created_at': '2024-01-01',
                'last_login': None,
                'failed_login_attempts': 0,
                'locked_until': None
            }
        ],
        'articles': [
            {
                'id': 1,
                'title': 'Introduction to Moroccan Law',
                'title_ar': 'مقدمة في القانون المغربي',
                'slug': 'introduction-moroccan-law',
                'content': sanitize_input('''<h2>مقدمة في القانون المغربي</h2>
                <p>يعتبر النظام القانوني المغربي نظاماً متميزاً يجمع بين التراث الإسلامي والقانون الحديث.</p>'''),
                'excerpt': 'تعرف على أساسيات النظام القانوني المغربي ومصادره المختلفة وتطوره عبر التاريخ',
                'category': 'القانون العام',
                'tags': ['قانون مغربي', 'نظام قانوني', 'شريعة إسلامية'],
                'is_published': True,
                'is_featured': True,
                'author_id': 1,
                'views': 1250,
                'created_at': '2024-01-15',
                'published_at': '2024-01-15',
                'meta_title': 'مقدمة شاملة في القانون المغربي - الأسس والمصادر',
                'meta_description': 'دليل شامل للنظام القانوني المغربي يغطي المصادر والأسس والتطور التاريخي'
            }
        ],
        'jurisprudence': [
            {
                'id': 1,
                'title': 'قرار محكمة النقض في قضية العقار',
                'case_number': 'نقض مدني رقم 123/2024',
                'court': 'محكمة النقض',
                'date': '2024-01-20',
                'summary': 'قرار مهم حول تفسير قانون العقار في المغرب',
                'legal_principle': 'لا يجوز التصرف في العقار المحفظ إلا بعد استكمال إجراءات التحفيظ',
                'category': 'القانون العقاري',
                'is_published': True
            }
        ],
        'consultations': [],
        'security_logs': [],
        'categories': [
            'القانون العام', 'القانون التجاري', 'قانون الأسرة', 'القانون العقاري',
            'القانون الجنائي', 'قانون العمل', 'القانون الإداري'
        ],
        'settings': {
            'site_name': 'موقع قانوني متخصص',
            'site_description': 'موقع قانوني متخصص يقدم المقالات والاستشارات القانونية',
            'contact_email': '<EMAIL>',
            'contact_phone': '+212 123 456 789',
            'address': 'الرباط، المغرب'
        }
    }

def save_data(data):
    """Save data to JSON file"""
    with open(DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def is_logged_in():
    """Check if user is logged in"""
    return 'user_id' in session and 'username' in session

def get_current_user():
    """Get current logged in user"""
    if is_logged_in():
        data = load_data()
        for user in data['users']:
            if user['id'] == session['user_id']:
                return user
    return None

def is_account_locked(user):
    """Check if account is locked due to failed login attempts"""
    if user.get('locked_until'):
        locked_until = datetime.fromisoformat(user['locked_until'])
        if datetime.now() < locked_until:
            return True
        else:
            # Unlock account
            user['locked_until'] = None
            user['failed_login_attempts'] = 0
    return False

def lock_account(user):
    """Lock account for 30 minutes after 5 failed attempts"""
    user['failed_login_attempts'] = user.get('failed_login_attempts', 0) + 1
    if user['failed_login_attempts'] >= 5:
        user['locked_until'] = (datetime.now() + timedelta(minutes=30)).isoformat()
        log_security_event('account_locked', user['id'], f"Account locked after {user['failed_login_attempts']} failed attempts", request.remote_addr)

def reset_failed_attempts(user):
    """Reset failed login attempts on successful login"""
    user['failed_login_attempts'] = 0
    user['locked_until'] = None

def validate_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def allowed_file(filename):
    """Check if file extension is allowed"""
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_file_content(file):
    """Validate file content and size"""
    if not file or not file.filename:
        return False, "لم يتم اختيار ملف"

    if not allowed_file(file.filename):
        return False, "نوع الملف غير مسموح"

    # Check file size (already handled by Flask MAX_CONTENT_LENGTH)
    file.seek(0, 2)  # Seek to end
    size = file.tell()
    file.seek(0)  # Reset to beginning

    if size > app.config['MAX_CONTENT_LENGTH']:
        return False, "حجم الملف كبير جداً"

    return True, "الملف صالح"

# Security middleware
@app.before_request
def security_headers():
    """Add security headers to all responses"""
    # Check for suspicious patterns in request
    suspicious_patterns = ['<script', 'javascript:', 'vbscript:', 'onload=', 'onerror=']
    request_data = str(request.args) + str(request.form) + str(request.path)

    for pattern in suspicious_patterns:
        if pattern.lower() in request_data.lower():
            log_security_event('suspicious_request', None, f"Suspicious pattern detected: {pattern}", request.remote_addr)
            flash('طلب غير صالح', 'error')
            return redirect(url_for('index'))

@app.after_request
def after_request(response):
    """Add additional security headers"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    return response

# Routes with security enhancements
@app.route('/')
def index():
    data = load_data()
    articles = [a for a in data['articles'] if a['is_published']]
    featured_articles = [a for a in articles if a['is_featured']][:3]
    latest_articles = sorted(articles, key=lambda x: x['published_at'], reverse=True)[:6]

    return render_template('enhanced_index.html',
                         articles=latest_articles,
                         featured_articles=featured_articles,
                         settings=data['settings'])

@app.route('/login', methods=['GET', 'POST'])
@limiter.limit("5 per minute")  # Rate limit login attempts
def login():
    if request.method == 'POST':
        try:
            # Validate CSRF token
            validate_csrf(request.form.get('csrf_token'))
        except:
            log_security_event('csrf_violation', None, "CSRF token validation failed", request.remote_addr)
            flash('طلب غير صالح', 'error')
            return render_template('enhanced_login.html')

        username = sanitize_input(request.form.get('username', '').strip())
        password = request.form.get('password', '')

        if not username or not password:
            flash('اسم المستخدم وكلمة المرور مطلوبان', 'error')
            return render_template('enhanced_login.html')

        data = load_data()
        user = None

        for u in data['users']:
            if u['username'] == username:
                user = u
                break

        if user:
            # Check if account is locked
            if is_account_locked(user):
                log_security_event('login_attempt_locked_account', user['id'], f"Login attempt on locked account", request.remote_addr)
                flash('الحساب مقفل مؤقتاً. حاول مرة أخرى لاحقاً.', 'error')
                return render_template('enhanced_login.html')

            # Check password
            if check_password(password, user['password_hash']) and user['is_admin']:
                # Successful login
                reset_failed_attempts(user)
                user['last_login'] = datetime.now().isoformat()

                session['user_id'] = user['id']
                session['username'] = user['username']
                session.permanent = True

                save_data(data)

                log_security_event('login_success', user['id'], f"Successful login", request.remote_addr)
                flash('تم تسجيل الدخول بنجاح', 'success')
                return redirect(url_for('admin_dashboard'))
            else:
                # Failed login
                lock_account(user)
                save_data(data)
                log_security_event('login_failed', user['id'], f"Failed login attempt", request.remote_addr)
        else:
            log_security_event('login_failed_unknown_user', None, f"Login attempt with unknown username: {username}", request.remote_addr)

        flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('enhanced_login.html')

@app.route('/logout')
def logout():
    user_id = session.get('user_id')
    if user_id:
        log_security_event('logout', user_id, "User logged out", request.remote_addr)

    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('index'))

# Error handlers with security considerations
@app.errorhandler(404)
def not_found_error(error):
    log_security_event('404_error', session.get('user_id'), f"404 error for path: {request.path}", request.remote_addr)
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    log_security_event('500_error', session.get('user_id'), f"500 error", request.remote_addr)
    return render_template('errors/500.html'), 500

@app.errorhandler(403)
def forbidden_error(error):
    log_security_event('403_error', session.get('user_id'), f"403 error for path: {request.path}", request.remote_addr)
    return render_template('errors/403.html'), 403

if __name__ == '__main__':
    # Initialize data with hashed passwords
    data = load_data()
    save_data(data)

    print("🔒 Secure Enhanced App Started")
    print("⚠️  Security features enabled:")
    print("   ✅ Password hashing (bcrypt)")
    print("   ✅ CSRF protection")
    print("   ✅ Rate limiting")
    print("   ✅ Security headers")
    print("   ✅ Input sanitization")
    print("   ✅ Security logging")
    print("   ✅ Account locking")

    app.run(debug=False, host='127.0.0.1', port=5001)  # Different port for security
