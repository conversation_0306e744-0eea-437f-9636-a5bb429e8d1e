<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% endblock %} - لوحة التحكم</title>
    
    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-navy: #1e3a8a;
            --primary-gold: #d4af37;
            --light-gray: #f8f9fa;
            --dark-gray: #6b7280;
        }
        
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            direction: rtl;
            text-align: right;
            background-color: #f8f9fa;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, var(--primary-navy) 0%, #1e40af 100%);
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
            transform: translateX(-5px);
        }
        
        .sidebar .nav-link i {
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            min-height: 100vh;
        }
        
        .top-navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e9ecef;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border-radius: 0.75rem;
        }
        
        .btn-primary-custom {
            background-color: var(--primary-navy);
            border-color: var(--primary-navy);
            color: white;
        }
        
        .btn-primary-custom:hover {
            background-color: #1e40af;
            border-color: #1e40af;
            color: white;
        }
        
        .btn-gold {
            background-color: var(--primary-gold);
            border-color: var(--primary-gold);
            color: white;
        }
        
        .btn-gold:hover {
            background-color: #b8941f;
            border-color: #b8941f;
            color: white;
        }
        
        .text-primary-custom { color: var(--primary-navy) !important; }
        .bg-primary-custom { background-color: var(--primary-navy) !important; }
        .text-gold { color: var(--primary-gold) !important; }
        .bg-gold { background-color: var(--primary-gold) !important; }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
        }
        
        .stats-card.gold {
            background: linear-gradient(135deg, var(--primary-gold) 0%, #b8941f 100%);
        }
        
        .stats-card.success {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
        }
        
        .stats-card.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                height: 100vh;
                z-index: 1050;
                transition: right 0.3s ease;
            }
            
            .sidebar.show {
                right: 0;
            }
            
            .main-content {
                margin-right: 0;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse" id="sidebarMenu">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white fw-bold">
                            <i class="bi bi-shield-lock me-2"></i>
                            لوحة التحكم
                        </h4>
                        <small class="text-white-50">مرحباً {{ current_user.username }}</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin_dashboard' %}active{% endif %}" 
                               href="{{ url_for('admin_dashboard') }}">
                                <i class="bi bi-speedometer2 me-2"></i>
                                لوحة المعلومات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'articles' in request.endpoint %}active{% endif %}" 
                               href="{{ url_for('admin_articles') }}">
                                <i class="bi bi-journal-text me-2"></i>
                                إدارة المقالات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'jurisprudence' in request.endpoint %}active{% endif %}" 
                               href="{{ url_for('admin_jurisprudence') }}">
                                <i class="bi bi-bank me-2"></i>
                                إدارة الاجتهادات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'consultations' in request.endpoint %}active{% endif %}" 
                               href="{{ url_for('admin_consultations') }}">
                                <i class="bi bi-chat-dots me-2"></i>
                                الاستشارات
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'admin_settings' %}active{% endif %}" 
                               href="{{ url_for('admin_settings') }}">
                                <i class="bi bi-gear me-2"></i>
                                الإعدادات
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <hr class="text-white-50">
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('index') }}" target="_blank">
                                <i class="bi bi-globe me-2"></i>
                                عرض الموقع
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>
                                تسجيل الخروج
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- Top Navbar -->
                <div class="top-navbar d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                    <h1 class="h2 text-primary-custom">{% block page_title %}{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button class="btn btn-outline-primary d-md-none" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#sidebarMenu">
                            <i class="bi bi-list"></i>
                        </button>
                        {% block page_actions %}{% endblock %}
                    </div>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} me-2"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Page Content -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-hide alerts
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
        
        // Confirm delete actions
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn-delete') || e.target.closest('.btn-delete')) {
                if (!confirm('هل أنت متأكد من الحذف؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                    e.preventDefault();
                    return false;
                }
            }
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
