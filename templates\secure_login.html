<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول الآمن - موقع قانوني متخصص</title>
    
    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta name="referrer" content="strict-origin-when-cross-origin">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #d4af37 100%);
            min-height: 100vh;
            font-family: 'Cairo', 'Noto Sans Arabic', sans-serif;
            direction: rtl;
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 400px;
            width: 100%;
            padding: 2rem;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #1e3a8a, #d4af37);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e5e7eb;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }
        
        .form-control:focus {
            border-color: #1e3a8a;
            box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25);
            background: white;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #1e3a8a, #1e40af);
            border: none;
            border-radius: 15px;
            padding: 0.75rem 2rem;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #1e40af, #1e3a8a);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(30, 58, 138, 0.3);
            color: white;
        }
        
        .security-notice {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            color: #065f46;
        }
        
        .security-features {
            font-size: 0.8rem;
            color: #6b7280;
            text-align: center;
            margin-top: 1rem;
        }
        
        .security-features i {
            color: #10b981;
            margin-left: 0.5rem;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 1rem;
        }
        
        .password-strength {
            height: 4px;
            border-radius: 2px;
            margin-top: 0.5rem;
            transition: all 0.3s ease;
        }
        
        .strength-weak { background: #ef4444; }
        .strength-medium { background: #f59e0b; }
        .strength-strong { background: #10b981; }
        
        .login-attempts {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #dc2626;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-logo">
                    <i class="bi bi-shield-lock"></i>
                </div>
                <h2 class="text-primary fw-bold mb-1">تسجيل الدخول الآمن</h2>
                <p class="text-muted">لوحة التحكم الإدارية</p>
            </div>
            
            <!-- Security Notice -->
            <div class="security-notice">
                <i class="bi bi-shield-check me-2"></i>
                <strong>اتصال آمن:</strong> جميع البيانات محمية بتشفير متقدم
            </div>
            
            <!-- Flash Messages -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            <i class="bi bi-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' }} me-2"></i>
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}
            
            <!-- Login Form -->
            <form method="POST" id="loginForm" novalidate>
                <!-- CSRF Token -->
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                
                <div class="mb-3">
                    <label for="username" class="form-label fw-semibold">
                        <i class="bi bi-person me-2"></i>اسم المستخدم
                    </label>
                    <input type="text" 
                           class="form-control" 
                           id="username" 
                           name="username" 
                           required 
                           autocomplete="username"
                           maxlength="50"
                           pattern="[a-zA-Z0-9_]{3,50}"
                           title="اسم المستخدم يجب أن يكون 3-50 حرف (أحرف وأرقام فقط)">
                    <div class="invalid-feedback">
                        يرجى إدخال اسم مستخدم صالح (3-50 حرف)
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label fw-semibold">
                        <i class="bi bi-lock me-2"></i>كلمة المرور
                    </label>
                    <div class="position-relative">
                        <input type="password" 
                               class="form-control" 
                               id="password" 
                               name="password" 
                               required 
                               autocomplete="current-password"
                               minlength="6"
                               maxlength="128">
                        <button type="button" 
                                class="btn btn-outline-secondary position-absolute end-0 top-0 h-100 px-3" 
                                id="togglePassword"
                                style="border-top-left-radius: 15px; border-bottom-left-radius: 15px;">
                            <i class="bi bi-eye" id="toggleIcon"></i>
                        </button>
                    </div>
                    <div class="password-strength" id="passwordStrength"></div>
                    <div class="invalid-feedback">
                        كلمة المرور يجب أن تكون 6 أحرف على الأقل
                    </div>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="rememberMe" name="remember_me">
                    <label class="form-check-label" for="rememberMe">
                        تذكرني لمدة أسبوع
                    </label>
                </div>
                
                <button type="submit" class="btn btn-login" id="loginBtn">
                    <i class="bi bi-box-arrow-in-right me-2"></i>
                    <span id="loginText">تسجيل الدخول</span>
                    <span id="loginSpinner" class="spinner-border spinner-border-sm d-none" role="status"></span>
                </button>
            </form>
            
            <!-- Security Features -->
            <div class="security-features">
                <div class="row text-center">
                    <div class="col-4">
                        <i class="bi bi-shield-lock"></i><br>
                        تشفير متقدم
                    </div>
                    <div class="col-4">
                        <i class="bi bi-eye-slash"></i><br>
                        حماية الخصوصية
                    </div>
                    <div class="col-4">
                        <i class="bi bi-clock"></i><br>
                        جلسة آمنة
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Security and UX enhancements
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            const togglePassword = document.getElementById('togglePassword');
            const toggleIcon = document.getElementById('toggleIcon');
            const loginBtn = document.getElementById('loginBtn');
            const loginText = document.getElementById('loginText');
            const loginSpinner = document.getElementById('loginSpinner');
            const passwordStrength = document.getElementById('passwordStrength');
            
            // Password visibility toggle
            togglePassword.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                toggleIcon.classList.toggle('bi-eye');
                toggleIcon.classList.toggle('bi-eye-slash');
            });
            
            // Password strength indicator
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                let strength = 0;
                
                if (password.length >= 6) strength++;
                if (password.match(/[a-z]/)) strength++;
                if (password.match(/[A-Z]/)) strength++;
                if (password.match(/[0-9]/)) strength++;
                if (password.match(/[^a-zA-Z0-9]/)) strength++;
                
                passwordStrength.className = 'password-strength';
                if (strength < 2) {
                    passwordStrength.classList.add('strength-weak');
                } else if (strength < 4) {
                    passwordStrength.classList.add('strength-medium');
                } else {
                    passwordStrength.classList.add('strength-strong');
                }
            });
            
            // Form validation and submission
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // Client-side validation
                const username = usernameInput.value.trim();
                const password = passwordInput.value;
                
                // Reset validation states
                form.classList.remove('was-validated');
                
                let isValid = true;
                
                // Username validation
                if (!username || username.length < 3 || username.length > 50) {
                    usernameInput.classList.add('is-invalid');
                    isValid = false;
                } else {
                    usernameInput.classList.remove('is-invalid');
                    usernameInput.classList.add('is-valid');
                }
                
                // Password validation
                if (!password || password.length < 6) {
                    passwordInput.classList.add('is-invalid');
                    isValid = false;
                } else {
                    passwordInput.classList.remove('is-invalid');
                    passwordInput.classList.add('is-valid');
                }
                
                if (isValid) {
                    // Show loading state
                    loginBtn.disabled = true;
                    loginText.classList.add('d-none');
                    loginSpinner.classList.remove('d-none');
                    
                    // Submit form
                    setTimeout(() => {
                        form.submit();
                    }, 500);
                } else {
                    form.classList.add('was-validated');
                }
            });
            
            // Security: Clear form on page unload
            window.addEventListener('beforeunload', function() {
                passwordInput.value = '';
            });
            
            // Security: Disable right-click and F12 in production
            // document.addEventListener('contextmenu', e => e.preventDefault());
            // document.addEventListener('keydown', e => {
            //     if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
            //         e.preventDefault();
            //     }
            // });
            
            // Auto-focus username field
            usernameInput.focus();
            
            // Rate limiting warning
            let attempts = parseInt(localStorage.getItem('loginAttempts') || '0');
            if (attempts >= 3) {
                const warning = document.createElement('div');
                warning.className = 'login-attempts';
                warning.innerHTML = '<i class="bi bi-exclamation-triangle me-2"></i>تحذير: عدة محاولات دخول فاشلة. قد يتم قفل الحساب.';
                form.insertBefore(warning, form.firstChild);
            }
            
            // Track failed attempts
            form.addEventListener('submit', function() {
                attempts++;
                localStorage.setItem('loginAttempts', attempts.toString());
            });
        });
    </script>
</body>
</html>
