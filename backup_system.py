"""
Advanced Backup System
Automated, encrypted, and scheduled backups
"""

import os
import json
import shutil
import sqlite3
import zipfile
import hashlib
import schedule
import time
import logging
from datetime import datetime, timedelta
from cryptography.fernet import Fernet
import threading

class BackupSystem:
    """Comprehensive backup system with encryption and scheduling"""
    
    def __init__(self, backup_dir='backups'):
        self.backup_dir = backup_dir
        self.encryption_key = self.load_or_generate_key()
        self.cipher_suite = Fernet(self.encryption_key)
        
        # Create backup directory
        os.makedirs(self.backup_dir, exist_ok=True)
        
        # Setup logging
        self.setup_logging()
        
        # Backup configuration
        self.config = {
            'max_backups': 30,  # Keep 30 backups
            'backup_schedule': {
                'daily': True,
                'weekly': True,
                'monthly': True
            },
            'backup_types': {
                'full': True,
                'incremental': True,
                'differential': False
            },
            'compression_level': 6,
            'encryption_enabled': True
        }
        
        # Files and directories to backup
        self.backup_items = [
            'enhanced_data.json',
            'secure_data.json',
            'security.log',
            'monitoring.db',
            'static/uploads/',
            'templates/',
            'static/css/',
            'static/js/',
            'static/images/',
            'logs/'
        ]
        
        # Files to exclude
        self.exclude_patterns = [
            '*.pyc',
            '__pycache__',
            '*.tmp',
            '*.log.old',
            'node_modules',
            '.git'
        ]
    
    def setup_logging(self):
        """Setup backup logging"""
        log_dir = 'logs'
        os.makedirs(log_dir, exist_ok=True)
        
        self.logger = logging.getLogger('backup_system')
        self.logger.setLevel(logging.INFO)
        
        # File handler
        file_handler = logging.FileHandler(f'{log_dir}/backup_system.log')
        file_handler.setLevel(logging.INFO)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def load_or_generate_key(self):
        """Load existing encryption key or generate new one"""
        key_file = 'backup_encryption.key'
        
        if os.path.exists(key_file):
            with open(key_file, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_file, 'wb') as f:
                f.write(key)
            self.logger.info("Generated new encryption key for backups")
            return key
    
    def create_backup_manifest(self, backup_path):
        """Create backup manifest with file checksums"""
        manifest = {
            'timestamp': datetime.now().isoformat(),
            'backup_type': 'full',
            'files': {},
            'total_size': 0,
            'file_count': 0
        }
        
        for root, dirs, files in os.walk('.'):
            for file in files:
                file_path = os.path.join(root, file)
                
                # Skip excluded files
                if self.should_exclude_file(file_path):
                    continue
                
                try:
                    # Calculate file hash
                    file_hash = self.calculate_file_hash(file_path)
                    file_size = os.path.getsize(file_path)
                    file_mtime = os.path.getmtime(file_path)
                    
                    manifest['files'][file_path] = {
                        'hash': file_hash,
                        'size': file_size,
                        'modified': file_mtime
                    }
                    
                    manifest['total_size'] += file_size
                    manifest['file_count'] += 1
                    
                except Exception as e:
                    self.logger.warning(f"Could not process file {file_path}: {e}")
        
        # Save manifest
        manifest_path = os.path.join(backup_path, 'manifest.json')
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)
        
        return manifest
    
    def should_exclude_file(self, file_path):
        """Check if file should be excluded from backup"""
        for pattern in self.exclude_patterns:
            if pattern.replace('*', '') in file_path:
                return True
        return False
    
    def calculate_file_hash(self, file_path):
        """Calculate SHA-256 hash of file"""
        hash_sha256 = hashlib.sha256()
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception:
            return None
    
    def create_full_backup(self):
        """Create full backup of all files"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"full_backup_{timestamp}"
        backup_path = os.path.join(self.backup_dir, backup_name)
        
        self.logger.info(f"Starting full backup: {backup_name}")
        
        try:
            # Create backup directory
            os.makedirs(backup_path, exist_ok=True)
            
            # Create manifest
            manifest = self.create_backup_manifest(backup_path)
            
            # Create compressed archive
            archive_path = f"{backup_path}.zip"
            
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED, 
                               compresslevel=self.config['compression_level']) as zipf:
                
                # Add files to archive
                for item in self.backup_items:
                    if os.path.exists(item):
                        if os.path.isfile(item):
                            zipf.write(item)
                        elif os.path.isdir(item):
                            for root, dirs, files in os.walk(item):
                                for file in files:
                                    file_path = os.path.join(root, file)
                                    if not self.should_exclude_file(file_path):
                                        zipf.write(file_path)
                
                # Add manifest
                zipf.write(os.path.join(backup_path, 'manifest.json'), 'manifest.json')
            
            # Encrypt backup if enabled
            if self.config['encryption_enabled']:
                encrypted_path = f"{archive_path}.encrypted"
                self.encrypt_file(archive_path, encrypted_path)
                os.remove(archive_path)  # Remove unencrypted version
                archive_path = encrypted_path
            
            # Remove temporary backup directory
            shutil.rmtree(backup_path)
            
            # Get final backup size
            backup_size = os.path.getsize(archive_path)
            
            self.logger.info(f"Full backup completed: {backup_name}")
            self.logger.info(f"Backup size: {backup_size / (1024*1024):.2f} MB")
            self.logger.info(f"Files backed up: {manifest['file_count']}")
            
            # Update backup log
            self.update_backup_log({
                'timestamp': datetime.now().isoformat(),
                'type': 'full',
                'name': backup_name,
                'size': backup_size,
                'file_count': manifest['file_count'],
                'encrypted': self.config['encryption_enabled'],
                'status': 'completed'
            })
            
            # Cleanup old backups
            self.cleanup_old_backups()
            
            return archive_path
            
        except Exception as e:
            self.logger.error(f"Full backup failed: {e}")
            return None
    
    def create_incremental_backup(self):
        """Create incremental backup (only changed files since last backup)"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"incremental_backup_{timestamp}"
        
        self.logger.info(f"Starting incremental backup: {backup_name}")
        
        try:
            # Get last backup manifest
            last_manifest = self.get_last_backup_manifest()
            if not last_manifest:
                self.logger.info("No previous backup found, creating full backup instead")
                return self.create_full_backup()
            
            # Find changed files
            changed_files = self.find_changed_files(last_manifest)
            
            if not changed_files:
                self.logger.info("No files changed since last backup")
                return None
            
            # Create incremental backup
            backup_path = os.path.join(self.backup_dir, backup_name)
            os.makedirs(backup_path, exist_ok=True)
            
            # Create archive with only changed files
            archive_path = f"{backup_path}.zip"
            
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED,
                               compresslevel=self.config['compression_level']) as zipf:
                
                for file_path in changed_files:
                    if os.path.exists(file_path):
                        zipf.write(file_path)
            
            # Encrypt if enabled
            if self.config['encryption_enabled']:
                encrypted_path = f"{archive_path}.encrypted"
                self.encrypt_file(archive_path, encrypted_path)
                os.remove(archive_path)
                archive_path = encrypted_path
            
            # Remove temporary directory
            shutil.rmtree(backup_path)
            
            backup_size = os.path.getsize(archive_path)
            
            self.logger.info(f"Incremental backup completed: {backup_name}")
            self.logger.info(f"Backup size: {backup_size / (1024*1024):.2f} MB")
            self.logger.info(f"Files backed up: {len(changed_files)}")
            
            # Update backup log
            self.update_backup_log({
                'timestamp': datetime.now().isoformat(),
                'type': 'incremental',
                'name': backup_name,
                'size': backup_size,
                'file_count': len(changed_files),
                'encrypted': self.config['encryption_enabled'],
                'status': 'completed'
            })
            
            return archive_path
            
        except Exception as e:
            self.logger.error(f"Incremental backup failed: {e}")
            return None
    
    def encrypt_file(self, input_path, output_path):
        """Encrypt file using Fernet encryption"""
        with open(input_path, 'rb') as infile:
            data = infile.read()
        
        encrypted_data = self.cipher_suite.encrypt(data)
        
        with open(output_path, 'wb') as outfile:
            outfile.write(encrypted_data)
    
    def decrypt_file(self, input_path, output_path):
        """Decrypt file using Fernet encryption"""
        with open(input_path, 'rb') as infile:
            encrypted_data = infile.read()
        
        decrypted_data = self.cipher_suite.decrypt(encrypted_data)
        
        with open(output_path, 'wb') as outfile:
            outfile.write(decrypted_data)
    
    def get_last_backup_manifest(self):
        """Get manifest from last backup"""
        backup_log = self.load_backup_log()
        if not backup_log:
            return None
        
        # Find last successful backup
        for backup in reversed(backup_log):
            if backup['status'] == 'completed':
                backup_file = os.path.join(self.backup_dir, f"{backup['name']}.zip")
                if backup['encrypted']:
                    backup_file += '.encrypted'
                
                if os.path.exists(backup_file):
                    try:
                        # Extract and read manifest
                        temp_dir = 'temp_manifest'
                        os.makedirs(temp_dir, exist_ok=True)
                        
                        if backup['encrypted']:
                            # Decrypt first
                            temp_zip = os.path.join(temp_dir, 'temp.zip')
                            self.decrypt_file(backup_file, temp_zip)
                            backup_file = temp_zip
                        
                        with zipfile.ZipFile(backup_file, 'r') as zipf:
                            zipf.extract('manifest.json', temp_dir)
                        
                        with open(os.path.join(temp_dir, 'manifest.json'), 'r') as f:
                            manifest = json.load(f)
                        
                        # Cleanup
                        shutil.rmtree(temp_dir)
                        
                        return manifest
                        
                    except Exception as e:
                        self.logger.error(f"Could not read manifest from {backup['name']}: {e}")
        
        return None
    
    def find_changed_files(self, last_manifest):
        """Find files that have changed since last backup"""
        changed_files = []
        
        for item in self.backup_items:
            if os.path.exists(item):
                if os.path.isfile(item):
                    if self.file_changed(item, last_manifest):
                        changed_files.append(item)
                elif os.path.isdir(item):
                    for root, dirs, files in os.walk(item):
                        for file in files:
                            file_path = os.path.join(root, file)
                            if not self.should_exclude_file(file_path):
                                if self.file_changed(file_path, last_manifest):
                                    changed_files.append(file_path)
        
        return changed_files
    
    def file_changed(self, file_path, last_manifest):
        """Check if file has changed since last backup"""
        if file_path not in last_manifest['files']:
            return True  # New file
        
        try:
            current_hash = self.calculate_file_hash(file_path)
            last_hash = last_manifest['files'][file_path]['hash']
            return current_hash != last_hash
        except Exception:
            return True  # Assume changed if can't calculate hash
    
    def update_backup_log(self, backup_info):
        """Update backup log with new backup information"""
        log_file = os.path.join(self.backup_dir, 'backup_log.json')
        
        backup_log = self.load_backup_log()
        backup_log.append(backup_info)
        
        with open(log_file, 'w') as f:
            json.dump(backup_log, f, indent=2)
    
    def load_backup_log(self):
        """Load backup log"""
        log_file = os.path.join(self.backup_dir, 'backup_log.json')
        
        if os.path.exists(log_file):
            with open(log_file, 'r') as f:
                return json.load(f)
        else:
            return []
    
    def cleanup_old_backups(self):
        """Remove old backups based on retention policy"""
        backup_log = self.load_backup_log()
        
        if len(backup_log) <= self.config['max_backups']:
            return
        
        # Sort by timestamp
        backup_log.sort(key=lambda x: x['timestamp'])
        
        # Remove oldest backups
        backups_to_remove = backup_log[:-self.config['max_backups']]
        
        for backup in backups_to_remove:
            backup_file = os.path.join(self.backup_dir, f"{backup['name']}.zip")
            if backup['encrypted']:
                backup_file += '.encrypted'
            
            if os.path.exists(backup_file):
                os.remove(backup_file)
                self.logger.info(f"Removed old backup: {backup['name']}")
        
        # Update log
        updated_log = backup_log[-self.config['max_backups']:]
        log_file = os.path.join(self.backup_dir, 'backup_log.json')
        with open(log_file, 'w') as f:
            json.dump(updated_log, f, indent=2)
    
    def restore_backup(self, backup_name, restore_path='.'):
        """Restore from backup"""
        backup_file = os.path.join(self.backup_dir, f"{backup_name}.zip")
        
        # Check if encrypted
        if not os.path.exists(backup_file):
            backup_file += '.encrypted'
            if not os.path.exists(backup_file):
                self.logger.error(f"Backup file not found: {backup_name}")
                return False
        
        self.logger.info(f"Restoring backup: {backup_name}")
        
        try:
            temp_zip = backup_file
            
            # Decrypt if needed
            if backup_file.endswith('.encrypted'):
                temp_zip = os.path.join(self.backup_dir, 'temp_restore.zip')
                self.decrypt_file(backup_file, temp_zip)
            
            # Extract backup
            with zipfile.ZipFile(temp_zip, 'r') as zipf:
                zipf.extractall(restore_path)
            
            # Cleanup temporary file
            if temp_zip != backup_file:
                os.remove(temp_zip)
            
            self.logger.info(f"Backup restored successfully: {backup_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Restore failed: {e}")
            return False
    
    def schedule_backups(self):
        """Schedule automatic backups"""
        if self.config['backup_schedule']['daily']:
            schedule.every().day.at("02:00").do(self.create_incremental_backup)
        
        if self.config['backup_schedule']['weekly']:
            schedule.every().sunday.at("03:00").do(self.create_full_backup)
        
        if self.config['backup_schedule']['monthly']:
            schedule.every().month.do(self.create_full_backup)
        
        self.logger.info("Backup schedule configured")
    
    def start_scheduler(self):
        """Start backup scheduler"""
        self.schedule_backups()
        
        def run_scheduler():
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        
        scheduler_thread = threading.Thread(target=run_scheduler)
        scheduler_thread.daemon = True
        scheduler_thread.start()
        
        self.logger.info("Backup scheduler started")
    
    def get_backup_status(self):
        """Get backup system status"""
        backup_log = self.load_backup_log()
        
        if not backup_log:
            return {
                'status': 'no_backups',
                'last_backup': None,
                'total_backups': 0,
                'total_size': 0
            }
        
        last_backup = backup_log[-1]
        total_size = sum(backup['size'] for backup in backup_log)
        
        return {
            'status': 'active',
            'last_backup': last_backup,
            'total_backups': len(backup_log),
            'total_size': total_size,
            'encryption_enabled': self.config['encryption_enabled'],
            'max_backups': self.config['max_backups']
        }

# Global backup system instance
backup_system = BackupSystem()

if __name__ == '__main__':
    # Create initial backup
    backup_system.create_full_backup()
    
    # Start scheduler
    backup_system.start_scheduler()
    
    print("Backup system started. Press Ctrl+C to stop.")
    
    try:
        while True:
            time.sleep(60)
            status = backup_system.get_backup_status()
            print(f"Backup Status: {status['total_backups']} backups, "
                  f"{status['total_size'] / (1024*1024):.2f} MB total")
    except KeyboardInterrupt:
        print("Backup system stopped")
