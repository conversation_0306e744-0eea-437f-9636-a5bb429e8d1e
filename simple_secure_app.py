from flask import Flask, render_template, request, redirect, url_for, flash, session
from datetime import datetime, timedelta
import json
import os
import re
import secrets
import hashlib
import logging

# Create Flask app with basic security
app = Flask(__name__)

# Security Configuration
app.config['SECRET_KEY'] = secrets.token_hex(32)
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(name)s %(message)s',
    handlers=[
        logging.FileHandler('security.log'),
        logging.StreamHandler()
    ]
)
security_logger = logging.getLogger('security')

# Simple data storage
DATA_FILE = 'secure_data.json'

def hash_password(password):
    """Hash password using SHA-256 (basic implementation)"""
    return hashlib.sha256(password.encode('utf-8')).hexdigest()

def check_password(password, hashed):
    """Check password against hash"""
    return hashlib.sha256(password.encode('utf-8')).hexdigest() == hashed

def sanitize_input(text):
    """Basic input sanitization"""
    if not text:
        return ""
    # Remove dangerous characters
    dangerous_chars = ['<', '>', '"', "'", '&', 'script', 'javascript', 'vbscript']
    for char in dangerous_chars:
        text = text.replace(char, '')
    return text.strip()

def log_security_event(event_type, user_id=None, details="", ip_address=None):
    """Log security events"""
    security_logger.warning(f"SECURITY: {event_type} | User: {user_id} | IP: {ip_address} | {details}")

def load_data():
    """Load data from JSON file"""
    if os.path.exists(DATA_FILE):
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    # Initialize with secure default data
    default_password = hash_password('JA138985kala@!!')
    return {
        'users': [
            {
                'id': 1,
                'username': 'mcmedo36',
                'password_hash': default_password,
                'email': '<EMAIL>',
                'is_admin': True,
                'created_at': '2024-01-01',
                'last_login': None,
                'failed_attempts': 0,
                'locked_until': None
            }
        ],
        'articles': [
            {
                'id': 1,
                'title_ar': 'مقدمة في القانون المغربي',
                'slug': 'introduction-moroccan-law',
                'content': '<h2>مقدمة في القانون المغربي</h2><p>يعتبر النظام القانوني المغربي نظاماً متميزاً.</p>',
                'excerpt': 'تعرف على أساسيات النظام القانوني المغربي',
                'category': 'القانون العام',
                'is_published': True,
                'views': 1250,
                'published_at': '2024-01-15'
            }
        ],
        'settings': {
            'site_name': 'موقع قانوني آمن',
            'site_description': 'موقع قانوني متخصص مع حماية أمنية متقدمة',
            'contact_email': '<EMAIL>'
        }
    }

def save_data(data):
    """Save data to JSON file"""
    with open(DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def is_logged_in():
    """Check if user is logged in"""
    return 'user_id' in session and 'username' in session

def get_current_user():
    """Get current logged in user"""
    if is_logged_in():
        data = load_data()
        for user in data['users']:
            if user['id'] == session['user_id']:
                return user
    return None

def is_account_locked(user):
    """Check if account is locked"""
    if user.get('locked_until'):
        try:
            locked_until = datetime.fromisoformat(user['locked_until'])
            if datetime.now() < locked_until:
                return True
            else:
                user['locked_until'] = None
                user['failed_attempts'] = 0
        except:
            pass
    return False

def validate_input(text, max_length=1000):
    """Validate and sanitize input"""
    if not text:
        return False, "النص مطلوب"
    
    if len(text) > max_length:
        return False, f"النص طويل جداً (الحد الأقصى {max_length} حرف)"
    
    # Check for suspicious patterns
    suspicious = ['<script', 'javascript:', 'vbscript:', 'onload=', 'onerror=']
    text_lower = text.lower()
    for pattern in suspicious:
        if pattern in text_lower:
            return False, "النص يحتوي على محتوى مشبوه"
    
    return True, sanitize_input(text)

# Security middleware
@app.before_request
def security_check():
    """Basic security checks"""
    # Check for suspicious patterns in request
    request_data = str(request.args) + str(request.form) + str(request.path)
    suspicious_patterns = ['<script', 'javascript:', 'vbscript:', 'onload=']
    
    for pattern in suspicious_patterns:
        if pattern.lower() in request_data.lower():
            log_security_event('suspicious_request', None, f"Pattern: {pattern}", request.remote_addr)
            flash('طلب غير صالح', 'error')
            return redirect(url_for('index'))

@app.after_request
def add_security_headers(response):
    """Add security headers"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    return response

# Routes
@app.route('/')
def index():
    data = load_data()
    articles = [a for a in data['articles'] if a['is_published']][:6]
    return render_template('enhanced_index.html',
                         articles=articles,
                         featured_articles=articles[:3],
                         settings=data['settings'])

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        
        # Basic validation
        if not username or not password:
            flash('اسم المستخدم وكلمة المرور مطلوبان', 'error')
            return render_template('enhanced_login.html')
        
        # Validate input
        valid, clean_username = validate_input(username, 50)
        if not valid:
            flash('اسم المستخدم غير صالح', 'error')
            return render_template('enhanced_login.html')
        
        data = load_data()
        user = None
        
        for u in data['users']:
            if u['username'] == clean_username:
                user = u
                break
        
        if user:
            # Check if account is locked
            if is_account_locked(user):
                log_security_event('login_locked', user['id'], "Locked account access attempt", request.remote_addr)
                flash('الحساب مقفل مؤقتاً. حاول مرة أخرى لاحقاً.', 'error')
                return render_template('enhanced_login.html')
            
            # Check password
            if check_password(password, user['password_hash']) and user['is_admin']:
                # Successful login
                user['failed_attempts'] = 0
                user['locked_until'] = None
                user['last_login'] = datetime.now().isoformat()
                
                session['user_id'] = user['id']
                session['username'] = user['username']
                session.permanent = True
                
                save_data(data)
                
                log_security_event('login_success', user['id'], "Successful login", request.remote_addr)
                flash('تم تسجيل الدخول بنجاح', 'success')
                return redirect(url_for('admin_dashboard'))
            else:
                # Failed login
                user['failed_attempts'] = user.get('failed_attempts', 0) + 1
                if user['failed_attempts'] >= 5:
                    user['locked_until'] = (datetime.now() + timedelta(minutes=30)).isoformat()
                    log_security_event('account_locked', user['id'], f"Account locked after {user['failed_attempts']} attempts", request.remote_addr)
                
                save_data(data)
                log_security_event('login_failed', user['id'], f"Failed login attempt #{user['failed_attempts']}", request.remote_addr)
        else:
            log_security_event('login_unknown', None, f"Unknown username: {clean_username}", request.remote_addr)
        
        flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('enhanced_login.html')

@app.route('/logout')
def logout():
    user_id = session.get('user_id')
    if user_id:
        log_security_event('logout', user_id, "User logged out", request.remote_addr)
    
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('index'))

@app.route('/medo36')
def admin_dashboard():
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))
    
    data = load_data()
    stats = {
        'total_articles': len(data['articles']),
        'published_articles': len([a for a in data['articles'] if a['is_published']]),
        'total_views': sum(a.get('views', 0) for a in data['articles'])
    }
    
    return render_template('enhanced_admin.html',
                         stats=stats,
                         recent_articles=data['articles'][:5],
                         current_user=get_current_user())

@app.route('/articles')
def articles():
    data = load_data()
    published_articles = [a for a in data['articles'] if a['is_published']]
    return render_template('enhanced_articles.html',
                         articles=published_articles,
                         categories=['القانون العام'],
                         current_search='',
                         current_category='')

@app.route('/article/<slug>')
def article_detail(slug):
    data = load_data()
    article = None
    
    for a in data['articles']:
        if a['slug'] == slug and a['is_published']:
            article = a
            a['views'] = a.get('views', 0) + 1
            save_data(data)
            break
    
    if not article:
        flash('المقال غير موجود', 'error')
        return redirect(url_for('articles'))
    
    return render_template('enhanced_article_detail.html',
                         article=article,
                         related_articles=[],
                         site_settings=data['settings'])

# Security monitoring endpoint (admin only)
@app.route('/medo36/security')
def security_monitor():
    if not is_logged_in():
        return redirect(url_for('login'))
    
    # Read security log
    security_events = []
    try:
        if os.path.exists('security.log'):
            with open('security.log', 'r') as f:
                lines = f.readlines()[-50:]  # Last 50 events
                for line in lines:
                    if 'SECURITY:' in line:
                        security_events.append(line.strip())
    except:
        pass
    
    return render_template('security_monitor.html', events=security_events)

if __name__ == '__main__':
    # Initialize data
    data = load_data()
    save_data(data)
    
    print("🔒 Simple Secure App Started")
    print("✅ Basic security features enabled:")
    print("   - Password hashing")
    print("   - Input sanitization") 
    print("   - Security logging")
    print("   - Account locking")
    print("   - Security headers")
    
    app.run(debug=False, host='127.0.0.1', port=5002)
