"""
Advanced Analytics System
Comprehensive website analytics and insights
"""

import json
import os
import sqlite3
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import logging
import threading
import time
from urllib.parse import urlparse, parse_qs
import re
import geoip2.database
import user_agents

class AdvancedAnalytics:
    """Comprehensive analytics system"""
    
    def __init__(self):
        self.db_path = 'analytics.db'
        self.session_timeout = 30  # minutes
        self.real_time_data = defaultdict(list)
        
        # Initialize database
        self.init_database()
        
        # Setup logging
        self.setup_logging()
        
        # Analytics configuration
        self.config = {
            'track_page_views': True,
            'track_user_sessions': True,
            'track_events': True,
            'track_conversions': True,
            'track_search_queries': True,
            'track_downloads': True,
            'track_form_submissions': True,
            'track_scroll_depth': True,
            'track_time_on_page': True,
            'track_bounce_rate': True,
            'track_referrers': True,
            'track_devices': True,
            'track_locations': True,
            'anonymize_ips': True
        }
        
        # Start real-time processing
        self.start_real_time_processing()
    
    def setup_logging(self):
        """Setup analytics logging"""
        log_dir = 'logs'
        os.makedirs(log_dir, exist_ok=True)
        
        self.logger = logging.getLogger('advanced_analytics')
        self.logger.setLevel(logging.INFO)
        
        # File handler
        file_handler = logging.FileHandler(f'{log_dir}/analytics.log')
        file_handler.setLevel(logging.INFO)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def init_database(self):
        """Initialize analytics database"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Page views table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS page_views (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                session_id TEXT,
                user_id TEXT,
                page_url TEXT,
                page_title TEXT,
                referrer TEXT,
                user_agent TEXT,
                ip_address TEXT,
                country TEXT,
                city TEXT,
                device_type TEXT,
                browser TEXT,
                os TEXT,
                screen_resolution TEXT,
                time_on_page INTEGER,
                scroll_depth INTEGER
            )
        ''')
        
        # User sessions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id TEXT UNIQUE,
                user_id TEXT,
                start_time DATETIME,
                end_time DATETIME,
                duration INTEGER,
                page_views INTEGER,
                bounce BOOLEAN,
                conversion BOOLEAN,
                traffic_source TEXT,
                campaign TEXT,
                medium TEXT,
                first_page TEXT,
                last_page TEXT,
                ip_address TEXT,
                user_agent TEXT
            )
        ''')
        
        # Events table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS events (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                session_id TEXT,
                user_id TEXT,
                event_category TEXT,
                event_action TEXT,
                event_label TEXT,
                event_value INTEGER,
                page_url TEXT,
                custom_data TEXT
            )
        ''')
        
        # Search queries table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS search_queries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                session_id TEXT,
                query TEXT,
                results_count INTEGER,
                clicked_result INTEGER,
                page_url TEXT
            )
        ''')
        
        # Conversions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                session_id TEXT,
                user_id TEXT,
                conversion_type TEXT,
                conversion_value REAL,
                page_url TEXT,
                funnel_step INTEGER,
                attribution_source TEXT
            )
        ''')
        
        # Performance metrics table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                page_url TEXT,
                load_time REAL,
                dom_ready_time REAL,
                first_paint_time REAL,
                largest_contentful_paint REAL,
                cumulative_layout_shift REAL,
                first_input_delay REAL
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def track_page_view(self, data):
        """Track page view"""
        if not self.config['track_page_views']:
            return
        
        # Extract and process data
        session_id = data.get('session_id')
        user_id = data.get('user_id')
        page_url = data.get('page_url', '')
        page_title = data.get('page_title', '')
        referrer = data.get('referrer', '')
        user_agent = data.get('user_agent', '')
        ip_address = self.anonymize_ip(data.get('ip_address', '')) if self.config['anonymize_ips'] else data.get('ip_address', '')
        
        # Parse user agent
        device_info = self.parse_user_agent(user_agent)
        
        # Get location info
        location_info = self.get_location_info(data.get('ip_address', ''))
        
        # Store in database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO page_views 
            (session_id, user_id, page_url, page_title, referrer, user_agent, 
             ip_address, country, city, device_type, browser, os, screen_resolution)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            session_id, user_id, page_url, page_title, referrer, user_agent,
            ip_address, location_info.get('country'), location_info.get('city'),
            device_info.get('device_type'), device_info.get('browser'), 
            device_info.get('os'), data.get('screen_resolution')
        ))
        
        conn.commit()
        conn.close()
        
        # Update real-time data
        self.real_time_data['page_views'].append({
            'timestamp': datetime.now().isoformat(),
            'page_url': page_url,
            'session_id': session_id
        })
        
        # Update session
        self.update_session(session_id, page_url, data)
        
        self.logger.info(f"Page view tracked: {page_url} (Session: {session_id})")
    
    def track_event(self, data):
        """Track custom event"""
        if not self.config['track_events']:
            return
        
        session_id = data.get('session_id')
        user_id = data.get('user_id')
        category = data.get('category', '')
        action = data.get('action', '')
        label = data.get('label', '')
        value = data.get('value', 0)
        page_url = data.get('page_url', '')
        custom_data = json.dumps(data.get('custom_data', {}))
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO events 
            (session_id, user_id, event_category, event_action, event_label, 
             event_value, page_url, custom_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (session_id, user_id, category, action, label, value, page_url, custom_data))
        
        conn.commit()
        conn.close()
        
        # Update real-time data
        self.real_time_data['events'].append({
            'timestamp': datetime.now().isoformat(),
            'category': category,
            'action': action,
            'session_id': session_id
        })
        
        self.logger.info(f"Event tracked: {category}/{action} (Session: {session_id})")
    
    def track_search_query(self, data):
        """Track search query"""
        if not self.config['track_search_queries']:
            return
        
        session_id = data.get('session_id')
        query = data.get('query', '')
        results_count = data.get('results_count', 0)
        clicked_result = data.get('clicked_result')
        page_url = data.get('page_url', '')
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO search_queries 
            (session_id, query, results_count, clicked_result, page_url)
            VALUES (?, ?, ?, ?, ?)
        ''', (session_id, query, results_count, clicked_result, page_url))
        
        conn.commit()
        conn.close()
        
        self.logger.info(f"Search query tracked: '{query}' (Session: {session_id})")
    
    def track_conversion(self, data):
        """Track conversion event"""
        if not self.config['track_conversions']:
            return
        
        session_id = data.get('session_id')
        user_id = data.get('user_id')
        conversion_type = data.get('type', '')
        conversion_value = data.get('value', 0.0)
        page_url = data.get('page_url', '')
        funnel_step = data.get('funnel_step', 1)
        attribution_source = data.get('attribution_source', '')
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO conversions 
            (session_id, user_id, conversion_type, conversion_value, 
             page_url, funnel_step, attribution_source)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (session_id, user_id, conversion_type, conversion_value, 
              page_url, funnel_step, attribution_source))
        
        conn.commit()
        conn.close()
        
        # Update session with conversion
        self.mark_session_conversion(session_id)
        
        self.logger.info(f"Conversion tracked: {conversion_type} (Session: {session_id})")
    
    def track_performance_metrics(self, data):
        """Track page performance metrics"""
        page_url = data.get('page_url', '')
        load_time = data.get('load_time', 0)
        dom_ready_time = data.get('dom_ready_time', 0)
        first_paint_time = data.get('first_paint_time', 0)
        largest_contentful_paint = data.get('largest_contentful_paint', 0)
        cumulative_layout_shift = data.get('cumulative_layout_shift', 0)
        first_input_delay = data.get('first_input_delay', 0)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO performance_metrics 
            (page_url, load_time, dom_ready_time, first_paint_time, 
             largest_contentful_paint, cumulative_layout_shift, first_input_delay)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (page_url, load_time, dom_ready_time, first_paint_time,
              largest_contentful_paint, cumulative_layout_shift, first_input_delay))
        
        conn.commit()
        conn.close()
    
    def update_session(self, session_id, page_url, data):
        """Update or create user session"""
        if not self.config['track_user_sessions']:
            return
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Check if session exists
        cursor.execute('SELECT * FROM user_sessions WHERE session_id = ?', (session_id,))
        session = cursor.fetchone()
        
        if session:
            # Update existing session
            cursor.execute('''
                UPDATE user_sessions 
                SET end_time = CURRENT_TIMESTAMP, 
                    page_views = page_views + 1,
                    last_page = ?
                WHERE session_id = ?
            ''', (page_url, session_id))
        else:
            # Create new session
            traffic_source = self.determine_traffic_source(data.get('referrer', ''))
            
            cursor.execute('''
                INSERT INTO user_sessions 
                (session_id, user_id, start_time, end_time, page_views, 
                 bounce, traffic_source, first_page, last_page, 
                 ip_address, user_agent)
                VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 1, 
                        1, ?, ?, ?, ?, ?)
            ''', (session_id, data.get('user_id'), traffic_source, 
                  page_url, page_url, data.get('ip_address'), data.get('user_agent')))
        
        conn.commit()
        conn.close()
    
    def mark_session_conversion(self, session_id):
        """Mark session as converted"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE user_sessions 
            SET conversion = 1 
            WHERE session_id = ?
        ''', (session_id,))
        
        conn.commit()
        conn.close()
    
    def anonymize_ip(self, ip_address):
        """Anonymize IP address for privacy"""
        if not ip_address:
            return ''
        
        # IPv4 anonymization (remove last octet)
        if '.' in ip_address:
            parts = ip_address.split('.')
            if len(parts) == 4:
                return '.'.join(parts[:3] + ['0'])
        
        # IPv6 anonymization (remove last 64 bits)
        if ':' in ip_address:
            parts = ip_address.split(':')
            if len(parts) >= 4:
                return ':'.join(parts[:4] + ['0000'] * (8 - 4))
        
        return ip_address
    
    def parse_user_agent(self, user_agent_string):
        """Parse user agent string"""
        try:
            ua = user_agents.parse(user_agent_string)
            return {
                'device_type': 'mobile' if ua.is_mobile else 'tablet' if ua.is_tablet else 'desktop',
                'browser': f"{ua.browser.family} {ua.browser.version_string}",
                'os': f"{ua.os.family} {ua.os.version_string}"
            }
        except:
            return {
                'device_type': 'unknown',
                'browser': 'unknown',
                'os': 'unknown'
            }
    
    def get_location_info(self, ip_address):
        """Get location information from IP address"""
        try:
            # This would require a GeoIP database
            # For now, return placeholder
            return {
                'country': 'Unknown',
                'city': 'Unknown'
            }
        except:
            return {
                'country': 'Unknown',
                'city': 'Unknown'
            }
    
    def determine_traffic_source(self, referrer):
        """Determine traffic source from referrer"""
        if not referrer:
            return 'direct'
        
        domain = urlparse(referrer).netloc.lower()
        
        # Search engines
        search_engines = ['google.', 'bing.', 'yahoo.', 'duckduckgo.', 'baidu.']
        if any(se in domain for se in search_engines):
            return 'search'
        
        # Social media
        social_media = ['facebook.', 'twitter.', 'linkedin.', 'instagram.', 'youtube.']
        if any(sm in domain for sm in social_media):
            return 'social'
        
        # Email
        if 'mail.' in domain or 'email.' in domain:
            return 'email'
        
        return 'referral'
    
    def get_analytics_dashboard_data(self, date_range=7):
        """Get dashboard analytics data"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=date_range)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Page views
        cursor.execute('''
            SELECT COUNT(*) as total_views,
                   COUNT(DISTINCT session_id) as unique_sessions,
                   COUNT(DISTINCT user_id) as unique_users
            FROM page_views 
            WHERE timestamp >= ? AND timestamp <= ?
        ''', (start_date, end_date))
        
        overview = cursor.fetchone()
        
        # Top pages
        cursor.execute('''
            SELECT page_url, COUNT(*) as views
            FROM page_views 
            WHERE timestamp >= ? AND timestamp <= ?
            GROUP BY page_url
            ORDER BY views DESC
            LIMIT 10
        ''', (start_date, end_date))
        
        top_pages = cursor.fetchall()
        
        # Traffic sources
        cursor.execute('''
            SELECT traffic_source, COUNT(*) as sessions
            FROM user_sessions 
            WHERE start_time >= ? AND start_time <= ?
            GROUP BY traffic_source
            ORDER BY sessions DESC
        ''', (start_date, end_date))
        
        traffic_sources = cursor.fetchall()
        
        # Device types
        cursor.execute('''
            SELECT device_type, COUNT(*) as views
            FROM page_views 
            WHERE timestamp >= ? AND timestamp <= ?
            GROUP BY device_type
            ORDER BY views DESC
        ''', (start_date, end_date))
        
        device_types = cursor.fetchall()
        
        # Bounce rate
        cursor.execute('''
            SELECT 
                COUNT(*) as total_sessions,
                SUM(CASE WHEN bounce = 1 THEN 1 ELSE 0 END) as bounced_sessions
            FROM user_sessions 
            WHERE start_time >= ? AND start_time <= ?
        ''', (start_date, end_date))
        
        bounce_data = cursor.fetchone()
        bounce_rate = (bounce_data[1] / bounce_data[0] * 100) if bounce_data[0] > 0 else 0
        
        # Conversion rate
        cursor.execute('''
            SELECT 
                COUNT(*) as total_sessions,
                SUM(CASE WHEN conversion = 1 THEN 1 ELSE 0 END) as converted_sessions
            FROM user_sessions 
            WHERE start_time >= ? AND start_time <= ?
        ''', (start_date, end_date))
        
        conversion_data = cursor.fetchone()
        conversion_rate = (conversion_data[1] / conversion_data[0] * 100) if conversion_data[0] > 0 else 0
        
        conn.close()
        
        return {
            'overview': {
                'total_page_views': overview[0],
                'unique_sessions': overview[1],
                'unique_users': overview[2],
                'bounce_rate': bounce_rate,
                'conversion_rate': conversion_rate
            },
            'top_pages': [{'url': row[0], 'views': row[1]} for row in top_pages],
            'traffic_sources': [{'source': row[0], 'sessions': row[1]} for row in traffic_sources],
            'device_types': [{'type': row[0], 'views': row[1]} for row in device_types],
            'date_range': {
                'start': start_date.isoformat(),
                'end': end_date.isoformat(),
                'days': date_range
            }
        }
    
    def get_real_time_data(self):
        """Get real-time analytics data"""
        # Clean old real-time data (older than 1 hour)
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        for data_type in self.real_time_data:
            self.real_time_data[data_type] = [
                item for item in self.real_time_data[data_type]
                if datetime.fromisoformat(item['timestamp']) > cutoff_time
            ]
        
        return {
            'active_users': len(set(item['session_id'] for item in self.real_time_data['page_views'])),
            'page_views_last_hour': len(self.real_time_data['page_views']),
            'events_last_hour': len(self.real_time_data['events']),
            'top_pages_now': Counter(item['page_url'] for item in self.real_time_data['page_views']).most_common(5),
            'recent_events': self.real_time_data['events'][-10:],  # Last 10 events
            'timestamp': datetime.now().isoformat()
        }
    
    def start_real_time_processing(self):
        """Start real-time data processing"""
        def process_real_time():
            while True:
                try:
                    # Process real-time data every 30 seconds
                    self.process_sessions()
                    time.sleep(30)
                except Exception as e:
                    self.logger.error(f"Real-time processing error: {e}")
                    time.sleep(30)
        
        processing_thread = threading.Thread(target=process_real_time)
        processing_thread.daemon = True
        processing_thread.start()
        
        self.logger.info("Real-time analytics processing started")
    
    def process_sessions(self):
        """Process and update session data"""
        # Update session durations and bounce rates
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Update session durations
        cursor.execute('''
            UPDATE user_sessions 
            SET duration = (julianday(end_time) - julianday(start_time)) * 24 * 60 * 60
            WHERE duration IS NULL
        ''')
        
        # Update bounce status (sessions with only 1 page view)
        cursor.execute('''
            UPDATE user_sessions 
            SET bounce = CASE WHEN page_views = 1 THEN 1 ELSE 0 END
            WHERE bounce IS NULL OR bounce = 1
        ''')
        
        conn.commit()
        conn.close()

# Global analytics instance
analytics = AdvancedAnalytics()

if __name__ == '__main__':
    # Test analytics
    test_data = {
        'session_id': 'test_session_123',
        'user_id': 'user_456',
        'page_url': '/articles/test-article',
        'page_title': 'Test Article',
        'referrer': 'https://google.com',
        'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'ip_address': '***********',
        'screen_resolution': '1920x1080'
    }
    
    analytics.track_page_view(test_data)
    
    # Get dashboard data
    dashboard_data = analytics.get_analytics_dashboard_data()
    print(json.dumps(dashboard_data, indent=2))
