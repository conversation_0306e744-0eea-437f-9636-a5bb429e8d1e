{% extends "enhanced_base.html" %}

{% block title %}المقالات{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb" class="bg-light py-2">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
            <li class="breadcrumb-item active">المقالات</li>
        </ol>
    </div>
</nav>
{% endblock %}

{% block content %}
<!-- Page Header -->
<section class="bg-primary-custom text-white py-4">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="display-5 fw-bold mb-2">المقالات القانونية</h1>
                <p class="lead mb-0">تصفح مجموعة شاملة من المقالات القانونية المتخصصة</p>
            </div>
        </div>
    </div>
</section>

<div class="container py-5">
    <!-- Search and Filter Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-6">
                            <label for="search" class="form-label fw-bold">البحث في المقالات</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="{{ current_search }}" placeholder="ابحث في العناوين والمحتوى...">
                        </div>
                        <div class="col-md-4">
                            <label for="category" class="form-label fw-bold">التصنيف</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">جميع التصنيفات</option>
                                {% for cat in categories %}
                                <option value="{{ cat }}" {% if cat == current_category %}selected{% endif %}>
                                    {{ cat }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary-custom w-100">
                                <i class="bi bi-search me-1"></i>بحث
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Info -->
    {% if current_search or current_category %}
    <div class="row mb-3">
        <div class="col-12">
            <div class="alert alert-info">
                <i class="bi bi-info-circle me-2"></i>
                عرض {{ articles|length }} نتيجة
                {% if current_search %}للبحث عن "{{ current_search }}"{% endif %}
                {% if current_category %}في تصنيف "{{ current_category }}"{% endif %}
                <a href="{{ url_for('articles') }}" class="btn btn-sm btn-outline-primary ms-3">
                    <i class="bi bi-x-circle me-1"></i>إزالة الفلاتر
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Articles Grid -->
    {% if articles %}
    <div class="row">
        {% for article in articles %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm card-hover">
                {% if article.featured_image %}
                <div class="card-img-top position-relative" style="height: 200px; overflow: hidden;">
                    <img src="{{ url_for('static', filename='uploads/articles/' + article.featured_image) }}"
                         alt="{{ article.title_ar }}"
                         class="w-100 h-100" style="object-fit: cover;">
                    <div class="position-absolute top-0 start-0 p-2">
                        {% if article.is_featured %}
                        <span class="badge bg-gold">مميز</span>
                        {% endif %}
                    </div>
                    <div class="position-absolute bottom-0 end-0 p-2">
                        <span class="badge bg-dark bg-opacity-75">
                            <i class="bi bi-eye me-1"></i>{{ article.views or 0 }}
                        </span>
                    </div>
                </div>
                {% else %}
                <div class="card-img-top bg-gradient position-relative"
                     style="height: 200px; background: linear-gradient(135deg, var(--primary-navy) 0%, #1e40af 100%);">
                    <div class="position-absolute top-50 start-50 translate-middle text-center">
                        <i class="bi bi-journal-text text-white display-4 mb-2"></i>
                    </div>
                    <div class="position-absolute top-0 end-0 p-2">
                        {% if article.is_featured %}
                        <span class="badge bg-gold">مميز</span>
                        {% endif %}
                    </div>
                    <div class="position-absolute bottom-0 end-0 p-2">
                        <span class="badge bg-dark bg-opacity-75">
                            <i class="bi bi-eye me-1"></i>{{ article.views or 0 }}
                        </span>
                    </div>
                </div>
                {% endif %}

                <div class="card-body d-flex flex-column">
                    <div class="mb-2">
                        <span class="badge bg-primary-custom">{{ article.category }}</span>
                        {% if article.is_featured %}
                        <span class="badge bg-gold ms-2">مميز</span>
                        {% endif %}
                    </div>

                    <h5 class="card-title fw-bold">
                        <a href="{{ url_for('article_detail', slug=article.slug) }}"
                           class="text-decoration-none text-dark">
                            {{ article.title_ar }}
                        </a>
                    </h5>

                    <p class="card-text text-muted flex-grow-1">
                        {{ article.excerpt[:150] }}{% if article.excerpt and article.excerpt|length > 150 %}...{% endif %}
                    </p>

                    <div class="article-meta mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="bi bi-calendar3 me-1"></i>
                                {{ article.published_at }}
                            </small>
                            <div class="view-count">
                                <i class="bi bi-eye text-muted"></i>
                                <small class="text-muted">{{ article.views or 0 }}</small>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mt-auto">
                        <div class="d-flex gap-1">
                            {% for tag in article.tags[:2] %}
                            <span class="badge bg-light text-dark small">#{{ tag }}</span>
                            {% endfor %}
                        </div>
                        <a href="{{ url_for('article_detail', slug=article.slug) }}"
                           class="btn btn-sm btn-outline-primary">
                            اقرأ المزيد
                            <i class="bi bi-arrow-left ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Load More Button (if needed) -->
    {% if articles|length >= 12 %}
    <div class="text-center mt-4">
        <button class="btn btn-outline-primary btn-lg" onclick="loadMoreArticles()">
            <i class="bi bi-arrow-down-circle me-2"></i>
            تحميل المزيد من المقالات
        </button>
    </div>
    {% endif %}

    {% else %}
    <!-- No Articles Found -->
    <div class="text-center py-5">
        <i class="bi bi-journal-text display-1 text-muted mb-3"></i>
        <h4 class="text-muted">لا توجد مقالات</h4>
        {% if current_search or current_category %}
        <p class="text-muted">لم يتم العثور على مقالات تطابق معايير البحث</p>
        <a href="{{ url_for('articles') }}" class="btn btn-primary-custom">
            <i class="bi bi-arrow-right me-2"></i>
            عرض جميع المقالات
        </a>
        {% else %}
        <p class="text-muted">تحقق مرة أخرى لاحقاً للاطلاع على المقالات الجديدة</p>
        <a href="{{ url_for('index') }}" class="btn btn-primary-custom">
            <i class="bi bi-house me-2"></i>
            العودة للرئيسية
        </a>
        {% endif %}
    </div>
    {% endif %}
</div>

<!-- Popular Categories Section -->
{% if categories %}
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h3 class="fw-bold text-primary-custom mb-3">التصنيفات القانونية</h3>
                <p class="text-muted">تصفح المقالات حسب التخصص القانوني</p>
            </div>
        </div>

        <div class="row">
            {% for category in categories %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-3">
                <a href="{{ url_for('articles') }}?category={{ category }}" class="text-decoration-none">
                    <div class="card border-0 shadow-sm card-hover text-center">
                        <div class="card-body">
                            <i class="bi bi-folder text-primary-custom display-6 mb-2"></i>
                            <h6 class="fw-bold text-dark">{{ category }}</h6>
                            <small class="text-muted">
                                تصفح المقالات
                            </small>
                        </div>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function loadMoreArticles() {
    // This would implement infinite scroll or pagination
    // For now, just show a message
    alert('ميزة تحميل المزيد قيد التطوير');
}

// Auto-submit form on category change
document.getElementById('category').addEventListener('change', function() {
    this.form.submit();
});
</script>
{% endblock %}
