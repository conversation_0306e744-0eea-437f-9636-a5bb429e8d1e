<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8860B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="navyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="#000000" flood-opacity="0.3"/>
    </filter>
  </defs>

  <!-- Background Circle -->
  <circle cx="16" cy="16" r="14" fill="url(#navyGradient)" filter="url(#shadow)"/>
  <circle cx="16" cy="16" r="12" fill="none" stroke="url(#goldGradient)" stroke-width="1.5"/>

  <!-- Gavel Icon -->
  <g transform="translate(16, 16)">
    <!-- Gavel Handle -->
    <rect x="-1" y="-6" width="2" height="8" fill="url(#goldGradient)" rx="1"/>

    <!-- Gavel Head -->
    <rect x="-4" y="-8" width="8" height="3" fill="url(#goldGradient)" rx="1.5"/>

    <!-- Base -->
    <ellipse cx="0" cy="3" rx="5" ry="1.5" fill="url(#goldGradient)" opacity="0.8"/>

    <!-- Decorative Star -->
    <polygon points="0,-3 0.5,-2.5 1,-2.5 0.5,-2 0.7,-1.5 0,-1.8 -0.7,-1.5 -0.5,-2 -1,-2.5 -0.5,-2.5"
             fill="url(#goldGradient)" opacity="0.7"/>
  </g>
</svg>
