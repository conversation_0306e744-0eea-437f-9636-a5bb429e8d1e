{% extends "enhanced_base.html" %}

{% block title %}{{ article.title_ar }}{% endblock %}
{% block meta_description %}{{ article.meta_description or article.excerpt }}{% endblock %}
{% block meta_keywords %}{{ article.tags|join(', ') }}{% endblock %}

{% block og_title %}{{ article.title_ar }} - {{ site_settings.site_name }}{% endblock %}
{% block og_description %}{{ article.excerpt }}{% endblock %}
{% block og_type %}article{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb" class="bg-light py-2">
    <div class="container">
        <ol class="breadcrumb mb-0">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('articles') }}">المقالات</a></li>
            <li class="breadcrumb-item active">{{ article.title_ar }}</li>
        </ol>
    </div>
</nav>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <!-- Main Article Content -->
        <div class="col-lg-8">
            <article class="card border-0 shadow-sm">
                <!-- Article Header -->
                <div class="card-header bg-white border-0 py-4">
                    <div class="mb-3">
                        <span class="badge bg-primary-custom fs-6 px-3 py-2">{{ article.category }}</span>
                        {% if article.is_featured %}
                        <span class="badge bg-gold fs-6 px-3 py-2 ms-2">مميز</span>
                        {% endif %}
                    </div>

                    <h1 class="display-5 fw-bold text-primary-custom mb-3">{{ article.title_ar }}</h1>

                    {% if article.excerpt %}
                    <p class="lead text-muted mb-4">{{ article.excerpt }}</p>
                    {% endif %}

                    <!-- Article Meta -->
                    <div class="d-flex flex-wrap align-items-center gap-3 text-muted">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-calendar3 me-2"></i>
                            <span>{{ article.published_at }}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="bi bi-eye me-2"></i>
                            <span>{{ article.views or 0 }} مشاهدة</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="bi bi-clock me-2"></i>
                            <span>{{ (article.content|length / 200)|round|int }} دقائق قراءة</span>
                        </div>
                    </div>

                    <!-- Tags -->
                    {% if article.tags %}
                    <div class="mt-3">
                        {% for tag in article.tags %}
                        <span class="badge bg-light text-dark me-1">#{{ tag }}</span>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

                <!-- Featured Image -->
                {% if article.featured_image %}
                <div class="card-body border-bottom">
                    <div class="text-center">
                        <img src="{{ url_for('static', filename='uploads/articles/' + article.featured_image) }}"
                             alt="{{ article.title_ar }}"
                             class="img-fluid rounded shadow-sm"
                             style="max-height: 400px; width: auto;">
                    </div>
                </div>
                {% endif %}

                <!-- Article Content -->
                <div class="card-body">
                    <div class="article-content">
                        {{ article.content|safe }}
                    </div>
                </div>

                <!-- Article Footer -->
                <div class="card-footer bg-light border-0">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-2">شارك هذا المقال:</h6>
                            <div class="d-flex gap-2">
                                <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.url }}"
                                   target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-facebook"></i> فيسبوك
                                </a>
                                <a href="https://twitter.com/intent/tweet?url={{ request.url }}&text={{ article.title_ar }}"
                                   target="_blank" class="btn btn-outline-info btn-sm">
                                    <i class="bi bi-twitter"></i> تويتر
                                </a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ request.url }}"
                                   target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="bi bi-linkedin"></i> لينكدإن
                                </a>
                                <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard()">
                                    <i class="bi bi-link-45deg"></i> نسخ الرابط
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 text-md-end mt-3 mt-md-0">
                            <button class="btn btn-outline-primary btn-sm" onclick="window.print()">
                                <i class="bi bi-printer me-1"></i> طباعة
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="increaseFontSize()">
                                <i class="bi bi-zoom-in me-1"></i> تكبير النص
                            </button>
                        </div>
                    </div>
                </div>
            </article>

            <!-- Navigation Between Articles -->
            <div class="row mt-4">
                <div class="col-6">
                    <a href="#" class="btn btn-outline-primary w-100">
                        <i class="bi bi-arrow-right me-2"></i>
                        المقال السابق
                    </a>
                </div>
                <div class="col-6">
                    <a href="#" class="btn btn-outline-primary w-100">
                        المقال التالي
                        <i class="bi bi-arrow-left ms-2"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Table of Contents -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-primary-custom text-white">
                    <h6 class="mb-0">
                        <i class="bi bi-list-ul me-2"></i>
                        محتويات المقال
                    </h6>
                </div>
                <div class="card-body">
                    <div id="tableOfContents">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Related Articles -->
            {% if related_articles %}
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="bi bi-journal-text me-2"></i>
                        مقالات ذات صلة
                    </h6>
                </div>
                <div class="card-body p-0">
                    {% for related in related_articles %}
                    <div class="p-3 border-bottom">
                        <h6 class="fw-bold mb-2">
                            <a href="{{ url_for('article_detail', slug=related.slug) }}"
                               class="text-decoration-none text-dark">
                                {{ related.title_ar }}
                            </a>
                        </h6>
                        <p class="text-muted small mb-2">
                            {{ related.excerpt[:80] }}{% if related.excerpt|length > 80 %}...{% endif %}
                        </p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="bi bi-eye me-1"></i>{{ related.views or 0 }}
                            </small>
                            <small class="text-muted">{{ related.published_at }}</small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Quick Consultation -->
            <div class="card border-0 shadow-sm mb-4 bg-gradient">
                <div class="card-body text-center text-white"
                     style="background: linear-gradient(135deg, var(--primary-navy) 0%, #1e40af 100%);">
                    <i class="bi bi-chat-dots display-4 mb-3"></i>
                    <h5 class="fw-bold mb-3">هل تحتاج استشارة؟</h5>
                    <p class="mb-3">احصل على استشارة قانونية مجانية من خبرائنا</p>
                    <a href="{{ url_for('consultation') }}" class="btn btn-gold">
                        <i class="bi bi-arrow-left me-2"></i>
                        طلب استشارة
                    </a>
                </div>
            </div>

            <!-- Popular Articles -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="bi bi-fire me-2"></i>
                        المقالات الأكثر قراءة
                    </h6>
                </div>
                <div class="card-body p-0">
                    <!-- This would be populated with actual popular articles -->
                    <div class="p-3 border-bottom">
                        <div class="d-flex">
                            <div class="bg-primary-custom text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                 style="width: 30px; height: 30px; font-size: 0.8rem;">1</div>
                            <div>
                                <h6 class="fw-bold mb-1">مقدمة في القانون المغربي</h6>
                                <small class="text-muted">1,250 مشاهدة</small>
                            </div>
                        </div>
                    </div>
                    <div class="p-3 border-bottom">
                        <div class="d-flex">
                            <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                 style="width: 30px; height: 30px; font-size: 0.8rem;">2</div>
                            <div>
                                <h6 class="fw-bold mb-1">تحديثات مدونة الأسرة</h6>
                                <small class="text-muted">890 مشاهدة</small>
                            </div>
                        </div>
                    </div>
                    <div class="p-3">
                        <div class="d-flex">
                            <div class="bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                 style="width: 30px; height: 30px; font-size: 0.8rem;">3</div>
                            <div>
                                <h6 class="fw-bold mb-1">القانون التجاري المغربي</h6>
                                <small class="text-muted">750 مشاهدة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reading Progress Bar -->
<div class="progress position-fixed top-0 start-0 w-100" style="height: 3px; z-index: 1030;">
    <div class="progress-bar bg-gold" id="readingProgress" style="width: 0%"></div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .article-content {
        font-size: 1.1rem;
        line-height: 1.8;
        color: #333;
    }

    .article-content h2,
    .article-content h3,
    .article-content h4 {
        color: var(--primary-navy);
        margin-top: 2rem;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .article-content p {
        margin-bottom: 1.5rem;
        text-align: justify;
    }

    .article-content ul,
    .article-content ol {
        margin-bottom: 1.5rem;
        padding-right: 2rem;
    }

    .article-content li {
        margin-bottom: 0.5rem;
    }

    .article-content blockquote {
        border-right: 4px solid var(--primary-gold);
        padding-right: 1rem;
        margin: 2rem 0;
        font-style: italic;
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
    }

    .article-content strong {
        color: var(--primary-navy);
        font-weight: 600;
    }

    .font-size-small { font-size: 0.9rem !important; }
    .font-size-normal { font-size: 1.1rem !important; }
    .font-size-large { font-size: 1.3rem !important; }

    @media print {
        .card-header, .card-footer, .btn, nav, footer, .sidebar { display: none !important; }
        .article-content { font-size: 12pt; }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // Generate Table of Contents
    function generateTableOfContents() {
        const headings = document.querySelectorAll('.article-content h2, .article-content h3, .article-content h4');
        const toc = document.getElementById('tableOfContents');

        if (headings.length === 0) {
            toc.innerHTML = '<p class="text-muted small">لا توجد عناوين فرعية</p>';
            return;
        }

        let tocHTML = '<ul class="list-unstyled">';
        headings.forEach((heading, index) => {
            const id = `heading-${index}`;
            heading.id = id;
            const level = parseInt(heading.tagName.charAt(1));
            const indent = level > 2 ? 'ms-3' : '';

            tocHTML += `
                <li class="${indent} mb-2">
                    <a href="#${id}" class="text-decoration-none text-dark small">
                        ${heading.textContent}
                    </a>
                </li>
            `;
        });
        tocHTML += '</ul>';

        toc.innerHTML = tocHTML;
    }

    // Reading Progress
    function updateReadingProgress() {
        const article = document.querySelector('.article-content');
        const progressBar = document.getElementById('readingProgress');

        if (!article || !progressBar) return;

        const articleTop = article.offsetTop;
        const articleHeight = article.offsetHeight;
        const windowHeight = window.innerHeight;
        const scrollTop = window.pageYOffset;

        const progress = Math.min(
            Math.max((scrollTop - articleTop + windowHeight) / articleHeight * 100, 0),
            100
        );

        progressBar.style.width = progress + '%';
    }

    // Font Size Control
    let currentFontSize = 'normal';
    function increaseFontSize() {
        const content = document.querySelector('.article-content');
        content.classList.remove('font-size-small', 'font-size-normal', 'font-size-large');

        if (currentFontSize === 'normal') {
            content.classList.add('font-size-large');
            currentFontSize = 'large';
        } else if (currentFontSize === 'large') {
            content.classList.add('font-size-small');
            currentFontSize = 'small';
        } else {
            content.classList.add('font-size-normal');
            currentFontSize = 'normal';
        }
    }

    // Copy to Clipboard
    function copyToClipboard() {
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('تم نسخ الرابط بنجاح!');
        });
    }

    // Smooth scrolling for TOC links
    function setupSmoothScrolling() {
        document.querySelectorAll('#tableOfContents a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            });
        });
    }

    // Initialize everything when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        generateTableOfContents();
        setupSmoothScrolling();
        updateReadingProgress();

        // Update reading progress on scroll
        window.addEventListener('scroll', updateReadingProgress);
    });
</script>
{% endblock %}
