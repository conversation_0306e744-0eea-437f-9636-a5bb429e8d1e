from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from datetime import datetime
import json
import os
import re
from werkzeug.utils import secure_filename

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'dev-secret-key-change-in-production'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Simple data storage
DATA_FILE = 'enhanced_data.json'

def load_data():
    """Load data from JSON file"""
    if os.path.exists(DATA_FILE):
        with open(DATA_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return {
        'users': [
            {
                'id': 1,
                'username': 'mcmedo36',
                'password': 'JA138985kala@!!',
                'email': '<EMAIL>',
                'is_admin': True,
                'created_at': '2024-01-01',
                'last_login': None
            }
        ],
        'articles': [
            {
                'id': 1,
                'title': 'Introduction to Moroccan Law',
                'title_ar': 'مقدمة في القانون المغربي',
                'slug': 'introduction-moroccan-law',
                'content': '''<h2>مقدمة في القانون المغربي</h2>
                <p>يعتبر النظام القانوني المغربي نظاماً متميزاً يجمع بين التراث الإسلامي والقانون الحديث. يستند هذا النظام إلى مصادر متعددة تشمل:</p>
                <ul>
                <li><strong>الشريعة الإسلامية:</strong> المصدر الأساسي للقانون المغربي</li>
                <li><strong>القانون الوضعي:</strong> القوانين المستمدة من النظم القانونية الحديثة</li>
                <li><strong>العرف والتقاليد:</strong> الأعراف المحلية المتجذرة في المجتمع المغربي</li>
                </ul>
                <h3>تطور القانون المغربي</h3>
                <p>شهد القانون المغربي تطوراً مستمراً منذ الاستقلال، حيث تم إصدار العديد من القوانين الحديثة التي تواكب التطورات العالمية مع الحفاظ على الهوية المغربية.</p>''',
                'excerpt': 'تعرف على أساسيات النظام القانوني المغربي ومصادره المختلفة وتطوره عبر التاريخ',
                'category': 'القانون العام',
                'tags': ['قانون مغربي', 'نظام قانوني', 'شريعة إسلامية'],
                'is_published': True,
                'is_featured': True,
                'author_id': 1,
                'views': 1250,
                'created_at': '2024-01-15',
                'published_at': '2024-01-15',
                'meta_title': 'مقدمة شاملة في القانون المغربي - الأسس والمصادر',
                'meta_description': 'دليل شامل للنظام القانوني المغربي يغطي المصادر والأسس والتطور التاريخي'
            },
            {
                'id': 2,
                'title': 'Commercial Law in Morocco',
                'title_ar': 'القانون التجاري في المغرب',
                'slug': 'commercial-law-morocco',
                'content': '''<h2>القانون التجاري المغربي</h2>
                <p>ينظم القانون التجاري المغربي جميع الأنشطة التجارية والاقتصادية في المملكة، ويشمل:</p>
                <h3>قانون الشركات</h3>
                <ul>
                <li>الشركات ذات المسؤولية المحدودة (SARL)</li>
                <li>الشركات المساهمة (SA)</li>
                <li>الشركات التضامنية</li>
                <li>الشركات البسيطة</li>
                </ul>
                <h3>العقود التجارية</h3>
                <p>تخضع العقود التجارية لقواعد خاصة تختلف عن العقود المدنية، وتتميز بالسرعة والمرونة في التنفيذ.</p>
                <h3>الإفلاس والتسوية القضائية</h3>
                <p>ينظم القانون المغربي إجراءات الإفلاس والتسوية القضائية لحماية حقوق الدائنين والمدينين.</p>''',
                'excerpt': 'دليل شامل للقانون التجاري المغربي يغطي الشركات والعقود والإفلاس',
                'category': 'القانون التجاري',
                'tags': ['قانون تجاري', 'شركات', 'عقود تجارية'],
                'is_published': True,
                'is_featured': False,
                'author_id': 1,
                'views': 890,
                'created_at': '2024-01-10',
                'published_at': '2024-01-10',
                'meta_title': 'القانون التجاري المغربي - دليل شامل للشركات والعقود',
                'meta_description': 'تعرف على أحكام القانون التجاري المغربي وقوانين الشركات والعقود التجارية'
            },
            {
                'id': 3,
                'title': 'Family Law Updates',
                'title_ar': 'تحديثات مدونة الأسرة المغربية',
                'slug': 'family-law-updates',
                'content': '''<h2>آخر التطورات في مدونة الأسرة المغربية</h2>
                <p>شهدت مدونة الأسرة المغربية تطورات مهمة في السنوات الأخيرة، تهدف إلى تعزيز حقوق المرأة والطفل:</p>
                <h3>التعديلات الحديثة</h3>
                <ul>
                <li><strong>سن الزواج:</strong> رفع السن الأدنى للزواج</li>
                <li><strong>حقوق المرأة:</strong> تعزيز حقوق المرأة في الطلاق والحضانة</li>
                <li><strong>حماية الأطفال:</strong> تشديد القوانين لحماية حقوق الطفل</li>
                </ul>
                <h3>الإجراءات القضائية</h3>
                <p>تم تبسيط الإجراءات القضائية المتعلقة بقضايا الأسرة وتسريع البت فيها.</p>
                <h3>التطبيق العملي</h3>
                <p>يتطلب تطبيق هذه التعديلات تدريب القضاة والمحامين على الأحكام الجديدة.</p>''',
                'excerpt': 'أحدث التطورات والتعديلات في مدونة الأسرة المغربية وتأثيرها على المجتمع',
                'category': 'قانون الأسرة',
                'tags': ['مدونة الأسرة', 'حقوق المرأة', 'حقوق الطفل'],
                'is_published': True,
                'is_featured': True,
                'author_id': 1,
                'views': 2100,
                'created_at': '2024-01-05',
                'published_at': '2024-01-05',
                'meta_title': 'تحديثات مدونة الأسرة المغربية 2024 - آخر التطورات',
                'meta_description': 'اطلع على أحدث التعديلات في مدونة الأسرة المغربية وتأثيرها على حقوق المرأة والطفل'
            }
        ],
        'jurisprudence': [
            {
                'id': 1,
                'title': 'قرار محكمة النقض في قضية العقار',
                'case_number': 'نقض مدني رقم 123/2024',
                'court': 'محكمة النقض',
                'date': '2024-01-20',
                'summary': 'قرار مهم حول تفسير قانون العقار في المغرب',
                'legal_principle': 'لا يجوز التصرف في العقار المحفظ إلا بعد استكمال إجراءات التحفيظ',
                'category': 'القانون العقاري',
                'is_published': True
            }
        ],
        'consultations': [],
        'categories': [
            'القانون العام', 'القانون التجاري', 'قانون الأسرة', 'القانون العقاري',
            'القانون الجنائي', 'قانون العمل', 'القانون الإداري'
        ],
        'settings': {
            'site_name': 'موقع قانوني متخصص',
            'site_description': 'موقع قانوني متخصص يقدم المقالات والاستشارات القانونية',
            'contact_email': '<EMAIL>',
            'contact_phone': '+212 123 456 789',
            'address': 'الرباط، المغرب'
        }
    }

def save_data(data):
    """Save data to JSON file"""
    with open(DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def is_logged_in():
    """Check if user is logged in"""
    return 'user_id' in session

def get_current_user():
    """Get current logged in user"""
    if is_logged_in():
        data = load_data()
        for user in data['users']:
            if user['id'] == session['user_id']:
                return user
    return None

def generate_slug(title):
    """Generate URL-friendly slug from title"""
    # Remove special characters and convert to lowercase
    slug = re.sub(r'[^\w\s-]', '', title.lower())
    # Replace spaces with hyphens
    slug = re.sub(r'[-\s]+', '-', slug)
    return slug.strip('-')

def allowed_file(filename):
    """Check if file extension is allowed"""
    ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def allowed_image_file(filename):
    """Check if file is an allowed image"""
    IMAGE_EXTENSIONS = {'jpg', 'jpeg', 'png', 'gif', 'webp'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in IMAGE_EXTENSIONS

def save_uploaded_image(file, folder='articles'):
    """Save uploaded image and return filename"""
    if file and file.filename and allowed_image_file(file.filename):
        filename = secure_filename(file.filename)
        # Add timestamp to avoid conflicts
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
        filename = timestamp + filename

        # Create folder if it doesn't exist
        upload_path = os.path.join('static', 'uploads', folder)
        os.makedirs(upload_path, exist_ok=True)

        file_path = os.path.join(upload_path, filename)
        file.save(file_path)
        return filename
    return None

# Routes
@app.route('/')
def index():
    data = load_data()
    articles = [a for a in data['articles'] if a['is_published']]
    featured_articles = [a for a in articles if a['is_featured']][:3]
    latest_articles = sorted(articles, key=lambda x: x['published_at'], reverse=True)[:6]

    return render_template('enhanced_index.html',
                         articles=latest_articles,
                         featured_articles=featured_articles,
                         settings=data['settings'])

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        data = load_data()
        for user in data['users']:
            if user['username'] == username and user['password'] == password and user['is_admin']:
                session['user_id'] = user['id']
                session['username'] = user['username']

                # Update last login
                user['last_login'] = datetime.now().isoformat()
                save_data(data)

                flash('تم تسجيل الدخول بنجاح', 'success')
                return redirect(url_for('admin_dashboard'))

        flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('enhanced_login.html')

@app.route('/logout')
def logout():
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'info')
    return redirect(url_for('index'))

@app.route('/medo36')
def admin_dashboard():
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    data = load_data()
    stats = {
        'total_articles': len(data['articles']),
        'published_articles': len([a for a in data['articles'] if a['is_published']]),
        'total_consultations': len(data['consultations']),
        'pending_consultations': len([c for c in data['consultations'] if c.get('status') == 'pending']),
        'total_views': sum(a.get('views', 0) for a in data['articles']),
        'total_jurisprudence': len(data['jurisprudence'])
    }

    # Recent articles
    recent_articles = sorted(data['articles'], key=lambda x: x['created_at'], reverse=True)[:5]

    return render_template('enhanced_admin.html',
                         stats=stats,
                         recent_articles=recent_articles,
                         current_user=get_current_user())

@app.route('/articles')
def articles():
    data = load_data()
    search = request.args.get('search', '')
    category = request.args.get('category', '')

    published_articles = [a for a in data['articles'] if a['is_published']]

    # Apply search filter
    if search:
        published_articles = [a for a in published_articles
                            if search.lower() in a['title_ar'].lower()
                            or search.lower() in a['content'].lower()]

    # Apply category filter
    if category:
        published_articles = [a for a in published_articles if a['category'] == category]

    published_articles = sorted(published_articles, key=lambda x: x['published_at'], reverse=True)

    return render_template('enhanced_articles.html',
                         articles=published_articles,
                         categories=data['categories'],
                         current_search=search,
                         current_category=category)

@app.route('/article/<slug>')
def article_detail(slug):
    data = load_data()
    article = None

    for a in data['articles']:
        if a['slug'] == slug and a['is_published']:
            article = a
            # Increment view count
            a['views'] = a.get('views', 0) + 1
            save_data(data)
            break

    if not article:
        flash('المقال غير موجود', 'error')
        return redirect(url_for('articles'))

    # Get related articles
    related_articles = [a for a in data['articles']
                       if a['is_published'] and a['category'] == article['category']
                       and a['id'] != article['id']][:3]

    return render_template('enhanced_article_detail.html',
                         article=article,
                         related_articles=related_articles)

@app.route('/jurisprudence')
def jurisprudence():
    data = load_data()
    search = request.args.get('search', '')

    cases = [j for j in data['jurisprudence'] if j['is_published']]

    if search:
        cases = [j for j in cases
                if search.lower() in j['title'].lower()
                or search.lower() in j['summary'].lower()]

    return render_template('enhanced_jurisprudence.html',
                         cases=cases,
                         current_search=search)

@app.route('/consultation', methods=['GET', 'POST'])
def consultation():
    if request.method == 'POST':
        data = load_data()

        # Get form data
        consultation_data = {
            'id': len(data['consultations']) + 1,
            'full_name': request.form['full_name'],
            'email': request.form['email'],
            'phone': request.form.get('phone', ''),
            'subject': request.form['subject'],
            'message': request.form['message'],
            'legal_field': request.form.get('legal_field', ''),
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'attachment': None
        }

        # Handle file upload
        if 'attachment' in request.files:
            file = request.files['attachment']
            if file and file.filename and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
                filename = timestamp + filename
                file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(file_path)
                consultation_data['attachment'] = filename

        data['consultations'].append(consultation_data)
        save_data(data)

        flash('تم إرسال طلب الاستشارة بنجاح. سنتواصل معك قريباً.', 'success')
        return redirect(url_for('consultation'))

    return render_template('enhanced_consultation.html')

@app.route('/about')
def about():
    return render_template('about.html')

@app.route('/contact')
def contact():
    return render_template('contact.html')

@app.route('/privacy')
def privacy():
    """Privacy Policy page"""
    data = load_data()
    return render_template('privacy.html',
                         site_settings=data['settings'])

@app.route('/terms')
def terms():
    """Terms of Service page"""
    data = load_data()
    return render_template('terms.html',
                         site_settings=data['settings'])

# Admin Routes
@app.route('/medo36/articles')
def admin_articles():
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    data = load_data()
    search = request.args.get('search', '')
    status = request.args.get('status', '')

    articles = data['articles']

    if search:
        articles = [a for a in articles
                   if search.lower() in a['title_ar'].lower()
                   or search.lower() in a['content'].lower()]

    if status == 'published':
        articles = [a for a in articles if a['is_published']]
    elif status == 'draft':
        articles = [a for a in articles if not a['is_published']]

    articles = sorted(articles, key=lambda x: x['created_at'], reverse=True)

    return render_template('admin/articles.html',
                         articles=articles,
                         current_search=search,
                         current_status=status,
                         categories=data['categories'])

@app.route('/medo36/articles/new', methods=['GET', 'POST'])
def admin_new_article():
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    if request.method == 'POST':
        data = load_data()

        # Generate slug from title
        title = request.form['title_ar']
        slug = title.lower().replace(' ', '-').replace('،', '').replace('؟', '').replace('!', '')

        # Ensure unique slug
        existing_slugs = [a['slug'] for a in data['articles']]
        original_slug = slug
        counter = 1
        while slug in existing_slugs:
            slug = f"{original_slug}-{counter}"
            counter += 1

        # Handle image upload
        featured_image = None
        if 'featured_image' in request.files:
            image_file = request.files['featured_image']
            featured_image = save_uploaded_image(image_file, 'articles')

        article_data = {
            'id': len(data['articles']) + 1,
            'title': request.form.get('title', ''),
            'title_ar': request.form['title_ar'],
            'slug': slug,
            'content': request.form['content'],
            'excerpt': request.form['excerpt'],
            'category': request.form['category'],
            'tags': [tag.strip() for tag in request.form['tags'].split(',') if tag.strip()],
            'is_published': 'is_published' in request.form,
            'is_featured': 'is_featured' in request.form,
            'author_id': get_current_user()['id'],
            'views': 0,
            'created_at': datetime.now().isoformat(),
            'published_at': datetime.now().isoformat() if 'is_published' in request.form else None,
            'meta_title': request.form.get('meta_title', ''),
            'meta_description': request.form.get('meta_description', ''),
            'featured_image': featured_image
        }

        data['articles'].append(article_data)
        save_data(data)

        flash('تم إنشاء المقال بنجاح', 'success')
        return redirect(url_for('admin_articles'))

    data = load_data()
    return render_template('admin/new_article.html', categories=data['categories'])

@app.route('/medo36/articles/edit/<int:article_id>', methods=['GET', 'POST'])
def admin_edit_article(article_id):
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    data = load_data()
    article = None

    for a in data['articles']:
        if a['id'] == article_id:
            article = a
            break

    if not article:
        flash('المقال غير موجود', 'error')
        return redirect(url_for('admin_articles'))

    if request.method == 'POST':
        # Handle image upload
        if 'featured_image' in request.files:
            image_file = request.files['featured_image']
            if image_file and image_file.filename:
                new_image = save_uploaded_image(image_file, 'articles')
                if new_image:
                    # Delete old image if exists
                    if article.get('featured_image'):
                        old_image_path = os.path.join('static', 'uploads', 'articles', article['featured_image'])
                        if os.path.exists(old_image_path):
                            os.remove(old_image_path)
                    article['featured_image'] = new_image

        article['title'] = request.form.get('title', '')
        article['title_ar'] = request.form['title_ar']
        article['content'] = request.form['content']
        article['excerpt'] = request.form['excerpt']
        article['category'] = request.form['category']
        article['tags'] = [tag.strip() for tag in request.form['tags'].split(',') if tag.strip()]
        article['is_published'] = 'is_published' in request.form
        article['is_featured'] = 'is_featured' in request.form
        article['meta_title'] = request.form.get('meta_title', '')
        article['meta_description'] = request.form.get('meta_description', '')

        if 'is_published' in request.form and not article.get('published_at'):
            article['published_at'] = datetime.now().isoformat()

        save_data(data)

        flash('تم تحديث المقال بنجاح', 'success')
        return redirect(url_for('admin_articles'))

    return render_template('admin/edit_article.html',
                         article=article,
                         categories=data['categories'])

@app.route('/medo36/articles/delete/<int:article_id>', methods=['POST'])
def admin_delete_article(article_id):
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    data = load_data()

    # Find article and delete its image if exists
    article_to_delete = None
    for a in data['articles']:
        if a['id'] == article_id:
            article_to_delete = a
            break

    if article_to_delete and article_to_delete.get('featured_image'):
        image_path = os.path.join('static', 'uploads', 'articles', article_to_delete['featured_image'])
        if os.path.exists(image_path):
            os.remove(image_path)

    data['articles'] = [a for a in data['articles'] if a['id'] != article_id]
    save_data(data)

    flash('تم حذف المقال بنجاح', 'success')
    return redirect(url_for('admin_articles'))

@app.route('/medo36/articles/<int:article_id>/delete-image', methods=['POST'])
def admin_delete_article_image(article_id):
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    data = load_data()
    article = None

    for a in data['articles']:
        if a['id'] == article_id:
            article = a
            break

    if article and article.get('featured_image'):
        # Delete image file
        image_path = os.path.join('static', 'uploads', 'articles', article['featured_image'])
        if os.path.exists(image_path):
            os.remove(image_path)

        # Remove image from article data
        article['featured_image'] = None
        save_data(data)

        flash('تم حذف الصورة بنجاح', 'success')
    else:
        flash('لا توجد صورة لحذفها', 'error')

    return redirect(url_for('admin_edit_article', article_id=article_id))

@app.route('/medo36/consultations')
def admin_consultations():
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    data = load_data()
    status = request.args.get('status', '')

    consultations = data['consultations']

    if status:
        consultations = [c for c in consultations if c.get('status') == status]

    consultations = sorted(consultations, key=lambda x: x['created_at'], reverse=True)

    return render_template('admin/consultations.html',
                         consultations=consultations,
                         current_status=status)

@app.route('/medo36/consultations/<int:consultation_id>')
def admin_consultation_detail(consultation_id):
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    data = load_data()
    consultation = None

    for c in data['consultations']:
        if c['id'] == consultation_id:
            consultation = c
            break

    if not consultation:
        flash('الاستشارة غير موجودة', 'error')
        return redirect(url_for('admin_consultations'))

    return render_template('admin/consultation_detail.html', consultation=consultation)

@app.route('/medo36/jurisprudence')
def admin_jurisprudence():
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    data = load_data()
    search = request.args.get('search', '')

    cases = data['jurisprudence']

    if search:
        cases = [j for j in cases
                if search.lower() in j['title'].lower()
                or search.lower() in j['summary'].lower()]

    cases = sorted(cases, key=lambda x: x['date'], reverse=True)

    return render_template('admin/jurisprudence.html',
                         cases=cases,
                         current_search=search)

@app.route('/medo36/jurisprudence/new', methods=['GET', 'POST'])
def admin_new_jurisprudence():
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    if request.method == 'POST':
        data = load_data()

        case_data = {
            'id': len(data['jurisprudence']) + 1,
            'title': request.form['title'],
            'case_number': request.form['case_number'],
            'court': request.form['court'],
            'date': request.form['date'],
            'summary': request.form['summary'],
            'legal_principle': request.form['legal_principle'],
            'category': request.form['category'],
            'is_published': 'is_published' in request.form
        }

        data['jurisprudence'].append(case_data)
        save_data(data)

        flash('تم إضافة الاجتهاد بنجاح', 'success')
        return redirect(url_for('admin_jurisprudence'))

    data = load_data()
    return render_template('admin/new_jurisprudence.html', categories=data['categories'])

@app.route('/medo36/jurisprudence/edit/<int:case_id>', methods=['GET', 'POST'])
def admin_edit_jurisprudence(case_id):
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    data = load_data()
    case = None

    for j in data['jurisprudence']:
        if j['id'] == case_id:
            case = j
            break

    if not case:
        flash('الاجتهاد غير موجود', 'error')
        return redirect(url_for('admin_jurisprudence'))

    if request.method == 'POST':
        case['title'] = request.form['title']
        case['case_number'] = request.form['case_number']
        case['court'] = request.form['court']
        case['date'] = request.form['date']
        case['summary'] = request.form['summary']
        case['legal_principle'] = request.form['legal_principle']
        case['category'] = request.form['category']
        case['is_published'] = 'is_published' in request.form

        save_data(data)

        flash('تم تحديث الاجتهاد بنجاح', 'success')
        return redirect(url_for('admin_jurisprudence'))

    return render_template('admin/edit_jurisprudence.html',
                         case=case,
                         categories=data['categories'])

@app.route('/medo36/jurisprudence/delete/<int:case_id>', methods=['POST'])
def admin_delete_jurisprudence(case_id):
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    data = load_data()
    data['jurisprudence'] = [j for j in data['jurisprudence'] if j['id'] != case_id]
    save_data(data)

    flash('تم حذف الاجتهاد بنجاح', 'success')
    return redirect(url_for('admin_jurisprudence'))

@app.route('/medo36/settings', methods=['GET', 'POST'])
def admin_settings():
    if not is_logged_in():
        flash('يرجى تسجيل الدخول أولاً', 'error')
        return redirect(url_for('login'))

    data = load_data()

    if request.method == 'POST':
        data['settings']['site_name'] = request.form['site_name']
        data['settings']['site_description'] = request.form['site_description']
        data['settings']['contact_email'] = request.form['contact_email']
        data['settings']['contact_phone'] = request.form['contact_phone']
        data['settings']['address'] = request.form['address']

        # Update categories
        categories_text = request.form['categories']
        data['categories'] = [cat.strip() for cat in categories_text.split('\n') if cat.strip()]

        save_data(data)

        flash('تم تحديث الإعدادات بنجاح', 'success')
        return redirect(url_for('admin_settings'))

    return render_template('admin/settings.html',
                         settings=data['settings'],
                         categories=data['categories'])

# API Routes
@app.route('/api/search')
def api_search():
    query = request.args.get('q', '').strip()
    if len(query) < 2:
        return jsonify({'results': []})

    data = load_data()
    results = []

    # Search in articles
    for article in data['articles']:
        if article['is_published'] and (
            query.lower() in article['title_ar'].lower() or
            query.lower() in article['content'].lower()
        ):
            results.append({
                'type': 'article',
                'title': article['title_ar'],
                'excerpt': article['excerpt'][:100] + '...',
                'url': url_for('article_detail', slug=article['slug']),
                'category': article['category']
            })

    return jsonify({'results': results[:5]})

# Template context processors
@app.context_processor
def inject_user():
    return dict(current_user=get_current_user())

@app.context_processor
def inject_data():
    data = load_data()
    return dict(site_settings=data['settings'])

if __name__ == '__main__':
    print("🚀 Starting Enhanced Legal Website...")
    print("📋 Admin credentials:")
    print("   Username: mcmedo36")
    print("   Password: JA138985kala@!!")
    print("🌐 Access the site at: http://localhost:5000")
    print("🔐 Admin panel at: http://localhost:5000/medo36")
    print("=" * 50)

    app.run(debug=True, host='0.0.0.0', port=5000)
