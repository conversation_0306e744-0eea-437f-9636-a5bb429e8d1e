<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 40" width="120" height="40">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8860B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="navyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e3a8a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="1" dy="1" stdDeviation="1" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>

  <!-- Outer Circle -->
  <circle cx="20" cy="20" r="16" fill="url(#navyGradient)" filter="url(#shadow)"/>
  <circle cx="20" cy="20" r="14" fill="none" stroke="url(#goldGradient)" stroke-width="1.5"/>

  <!-- Inner Symbol - Stylized Gavel -->
  <g transform="translate(20, 20)">
    <!-- Gavel Handle -->
    <rect x="-1.5" y="-8" width="3" height="12" fill="url(#goldGradient)" rx="1.5"/>

    <!-- Gavel Head -->
    <rect x="-5" y="-10" width="10" height="4" fill="url(#goldGradient)" rx="2"/>

    <!-- Base -->
    <ellipse cx="0" cy="5" rx="7" ry="2" fill="url(#goldGradient)" opacity="0.8"/>

    <!-- Decorative Star -->
    <polygon points="0,-4 0.5,-3 1.5,-3 0.5,-2 1,-1 0,-1.5 -1,-1 -0.5,-2 -1.5,-3 -0.5,-3"
             fill="url(#goldGradient)" opacity="0.6"/>
  </g>

  <!-- Arabic Text -->
  <text x="42" y="16" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="url(#navyGradient)">
    قانوني
  </text>
  <text x="42" y="28" font-family="Arial, sans-serif" font-size="8" fill="url(#navyGradient)" opacity="0.7">
    موقع متخصص
  </text>
</svg>
