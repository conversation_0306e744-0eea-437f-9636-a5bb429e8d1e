{% extends "enhanced_base.html" %}

{% block title %}الرئيسية{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section position-relative overflow-hidden">
    <!-- Animated Background -->
    <div class="hero-bg position-absolute w-100 h-100"></div>

    <!-- Floating Elements -->
    <div class="floating-elements position-absolute w-100 h-100">
        <div class="floating-icon" style="top: 10%; left: 10%; animation-delay: 0s;">
            <i class="bi bi-scales text-gold opacity-25 fs-1"></i>
        </div>
        <div class="floating-icon" style="top: 20%; right: 15%; animation-delay: 2s;">
            <i class="bi bi-gavel text-gold opacity-25 fs-2"></i>
        </div>
        <div class="floating-icon" style="bottom: 30%; left: 20%; animation-delay: 4s;">
            <i class="bi bi-book text-gold opacity-25 fs-3"></i>
        </div>
        <div class="floating-icon" style="bottom: 20%; right: 10%; animation-delay: 6s;">
            <i class="bi bi-shield-check text-gold opacity-25 fs-2"></i>
        </div>
    </div>

    <div class="container position-relative">
        <div class="row align-items-center min-vh-100 py-5">
            <div class="col-lg-6">
                <div class="hero-content">
                    <!-- Badge -->
                    <div class="badge bg-gold text-dark mb-3 px-3 py-2 rounded-pill fs-6 fade-in">
                        <i class="bi bi-star-fill me-1"></i>
                        موقع قانوني متخصص
                    </div>

                    <!-- Main Title -->
                    <h1 class="display-3 fw-bold mb-4 text-white fade-in">
                        مرحباً بكم في
                        <span class="text-gold">{{ site_settings.site_name }}</span>
                    </h1>

                    <!-- Description -->
                    <p class="lead mb-4 text-white-75 fs-5 fade-in">
                        {{ site_settings.site_description }}
                    </p>
                    <p class="mb-5 text-white-50 fade-in">
                        نقدم لكم أحدث المقالات القانونية والاجتهادات المغربية مع خدمة الاستشارات القانونية المتخصصة
                    </p>

                    <!-- Action Buttons -->
                    <div class="d-flex gap-3 flex-wrap mb-4 fade-in">
                        <a href="{{ url_for('consultation') }}" class="btn btn-gold btn-lg px-4 py-3 rounded-pill shadow-lg btn-hover">
                            <i class="bi bi-chat-dots me-2"></i>
                            استشارة قانونية مجانية
                        </a>
                        <a href="{{ url_for('articles') }}" class="btn btn-outline-light btn-lg px-4 py-3 rounded-pill btn-hover">
                            <i class="bi bi-journal-text me-2"></i>
                            تصفح المقالات
                        </a>
                    </div>

                    <!-- Stats -->
                    <div class="row g-3 mt-4 fade-in">
                        <div class="col-4">
                            <div class="text-center stats-item">
                                <div class="h2 fw-bold text-gold mb-1 counter" data-target="{{ articles|length }}">0</div>
                                <div class="small text-white-75">مقال قانوني</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center stats-item">
                                <div class="h2 fw-bold text-gold mb-1 counter" data-target="500">0</div>
                                <div class="small text-white-75">استشارة قانونية</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center stats-item">
                                <div class="h2 fw-bold text-gold mb-1 counter" data-target="5">0</div>
                                <div class="small text-white-75">سنوات خبرة</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="hero-visual position-relative">
                    <!-- Main Illustration -->
                    <div class="hero-illustration text-center fade-in">
                        <div class="main-icon-container position-relative mb-4">
                            <div class="icon-bg-pulse"></div>
                            <i class="bi bi-scales display-1 text-gold position-relative hero-main-icon"></i>
                        </div>

                        <!-- Feature Cards Grid -->
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="feature-card-modern p-4 h-100 card-hover">
                                    <div class="feature-icon mb-3">
                                        <i class="bi bi-book text-gold fs-2"></i>
                                    </div>
                                    <h6 class="fw-bold text-white mb-2">مقالات قانونية</h6>
                                    <p class="small text-white-75 mb-0">مقالات متخصصة في القانون المغربي</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-card-modern p-4 h-100 card-hover">
                                    <div class="feature-icon mb-3">
                                        <i class="bi bi-gavel text-gold fs-2"></i>
                                    </div>
                                    <h6 class="fw-bold text-white mb-2">اجتهادات مغربية</h6>
                                    <p class="small text-white-75 mb-0">أحدث الاجتهادات القضائية</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-card-modern p-4 h-100 card-hover">
                                    <div class="feature-icon mb-3">
                                        <i class="bi bi-chat-square-text text-gold fs-2"></i>
                                    </div>
                                    <h6 class="fw-bold text-white mb-2">استشارات قانونية</h6>
                                    <p class="small text-white-75 mb-0">استشارات مجانية من خبراء</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="feature-card-modern p-4 h-100 card-hover">
                                    <div class="feature-icon mb-3">
                                        <i class="bi bi-shield-check text-gold fs-2"></i>
                                    </div>
                                    <h6 class="fw-bold text-white mb-2">خدمة موثوقة</h6>
                                    <p class="small text-white-75 mb-0">معلومات قانونية دقيقة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="scroll-indicator position-absolute bottom-0 start-50 translate-middle-x mb-4">
        <div class="scroll-arrow bounce">
            <i class="bi bi-chevron-down text-white opacity-75 fs-4"></i>
        </div>
    </div>
</section>

<!-- Featured Articles Section -->
{% if featured_articles %}
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold text-primary-custom mb-3">
                    <i class="bi bi-star-fill text-gold me-2"></i>
                    المقالات المميزة
                </h2>
                <p class="text-muted">أحدث وأهم المقالات القانونية المتخصصة</p>
            </div>
        </div>

        <div class="row">
            {% for article in featured_articles %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-sm card-hover">
                    {% if article.featured_image %}
                    <div class="card-img-top position-relative" style="height: 200px; overflow: hidden;">
                        <img src="{{ url_for('static', filename='uploads/articles/' + article.featured_image) }}"
                             alt="{{ article.title_ar }}"
                             class="w-100 h-100" style="object-fit: cover;">
                        <div class="position-absolute top-0 start-0 p-2">
                            <div class="badge bg-gold">مميز</div>
                        </div>
                        <div class="position-absolute bottom-0 end-0 p-2">
                            <span class="badge bg-dark bg-opacity-75">
                                <i class="bi bi-eye me-1"></i>{{ article.views or 0 }}
                            </span>
                        </div>
                    </div>
                    {% else %}
                    <div class="card-img-top bg-gradient position-relative"
                         style="height: 200px; background: linear-gradient(135deg, var(--primary-navy) 0%, #1e40af 100%);">
                        <div class="position-absolute top-50 start-50 translate-middle text-center">
                            <i class="bi bi-journal-text text-white display-4 mb-2"></i>
                            <div class="badge bg-gold">مميز</div>
                        </div>
                        <div class="position-absolute bottom-0 end-0 p-2">
                            <span class="badge bg-dark bg-opacity-75">
                                <i class="bi bi-eye me-1"></i>{{ article.views or 0 }}
                            </span>
                        </div>
                    </div>
                    {% endif %}

                    <div class="card-body d-flex flex-column">
                        <div class="mb-2">
                            <span class="badge bg-primary-custom">{{ article.category }}</span>
                            <span class="badge bg-gold ms-2">مميز</span>
                        </div>

                        <h5 class="card-title fw-bold">
                            <a href="{{ url_for('article_detail', slug=article.slug) }}"
                               class="text-decoration-none text-dark">
                                {{ article.title_ar }}
                            </a>
                        </h5>

                        <p class="card-text text-muted flex-grow-1">
                            {{ article.excerpt[:150] }}{% if article.excerpt|length > 150 %}...{% endif %}
                        </p>

                        <div class="article-meta mb-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="bi bi-calendar3 me-1"></i>
                                    {{ article.published_at }}
                                </small>
                                <div class="view-count">
                                    <i class="bi bi-eye text-muted"></i>
                                    <small class="text-muted">{{ article.views or 0 }}</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mt-auto">
                            <div class="d-flex gap-1">
                                {% for tag in article.tags[:2] %}
                                <span class="badge bg-light text-dark small">#{{ tag }}</span>
                                {% endfor %}
                            </div>
                            <a href="{{ url_for('article_detail', slug=article.slug) }}"
                               class="btn btn-sm btn-outline-primary">
                                اقرأ المزيد
                                <i class="bi bi-arrow-left ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Services Section -->
<section class="section-padding bg-light position-relative">
    <!-- Background Pattern -->
    <div class="position-absolute top-0 start-0 w-100 h-100 opacity-25">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#1e3a8a" stroke-width="1" opacity="0.1"/>
                </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
    </div>

    <div class="container position-relative">
        <!-- Section Header -->
        <div class="row mb-5">
            <div class="col-12 text-center">
                <div class="badge bg-primary-custom text-white mb-3 px-3 py-2 rounded-pill">
                    <i class="bi bi-briefcase me-1"></i>
                    خدماتنا المتميزة
                </div>
                <h2 class="display-5 fw-bold text-primary-custom mb-3">خدماتنا القانونية</h2>
                <p class="lead text-muted mb-0">نقدم مجموعة شاملة من الخدمات القانونية المتخصصة بأعلى معايير الجودة</p>
            </div>
        </div>

        <div class="row g-4">
            <!-- Service 1: Articles -->
            <div class="col-lg-4 col-md-6">
                <div class="enhanced-card h-100 text-center">
                    <div class="card-body">
                        <div class="service-icon bg-primary-custom text-white">
                            <i class="bi bi-journal-text fs-2"></i>
                        </div>
                        <h5 class="fw-bold mb-3 text-primary-custom">المقالات القانونية</h5>
                        <p class="text-muted mb-4">مقالات متخصصة تغطي جميع جوانب القانون المغربي والدولي بأسلوب واضح ومفهوم للجميع</p>

                        <!-- Features -->
                        <div class="mb-4">
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <div class="fw-bold text-primary-custom">{{ articles|length }}+</div>
                                        <small class="text-muted">مقال</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <div class="fw-bold text-success">يومياً</div>
                                        <small class="text-muted">تحديث</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <a href="{{ url_for('articles') }}" class="btn btn-primary-custom btn-hover rounded-pill px-4">
                            <i class="bi bi-arrow-left me-2"></i>تصفح المقالات
                        </a>
                    </div>
                </div>
            </div>

            <!-- Service 2: Jurisprudence -->
            <div class="col-lg-4 col-md-6">
                <div class="enhanced-card h-100 text-center">
                    <div class="card-body">
                        <div class="service-icon bg-gold text-white">
                            <i class="bi bi-bank fs-2"></i>
                        </div>
                        <h5 class="fw-bold mb-3 text-primary-custom">الاجتهادات المغربية</h5>
                        <p class="text-muted mb-4">مجموعة شاملة من الاجتهادات والأحكام القضائية المغربية مع التحليل القانوني المتخصص</p>

                        <!-- Features -->
                        <div class="mb-4">
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <div class="fw-bold text-gold">100+</div>
                                        <small class="text-muted">اجتهاد</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <div class="fw-bold text-primary-custom">النقض</div>
                                        <small class="text-muted">محكمة</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <a href="{{ url_for('jurisprudence') }}" class="btn btn-gold btn-hover rounded-pill px-4">
                            <i class="bi bi-arrow-left me-2"></i>تصفح الاجتهادات
                        </a>
                    </div>
                </div>
            </div>

            <!-- Service 3: Consultations -->
            <div class="col-lg-4 col-md-6">
                <div class="enhanced-card h-100 text-center">
                    <div class="card-body">
                        <div class="service-icon bg-success text-white">
                            <i class="bi bi-chat-dots fs-2"></i>
                        </div>
                        <h5 class="fw-bold mb-3 text-primary-custom">الاستشارات القانونية</h5>
                        <p class="text-muted mb-4">استشارات قانونية متخصصة من خبراء في مختلف المجالات القانونية مع ضمان السرية التامة</p>

                        <!-- Features -->
                        <div class="mb-4">
                            <div class="row g-2">
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <div class="fw-bold text-success">مجانية</div>
                                        <small class="text-muted">استشارة</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="bg-light rounded p-2">
                                        <div class="fw-bold text-primary-custom">24</div>
                                        <small class="text-muted">ساعة</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <a href="{{ url_for('consultation') }}" class="btn btn-success btn-hover rounded-pill px-4">
                            <i class="bi bi-arrow-left me-2"></i>طلب استشارة
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Info -->
        <div class="row mt-5">
            <div class="col-12 text-center">
                <div class="bg-white rounded-3 p-4 shadow-sm">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="fw-bold text-primary-custom mb-2">هل تحتاج إلى خدمة قانونية مخصصة؟</h5>
                            <p class="text-muted mb-0">تواصل معنا للحصول على استشارة شخصية تناسب احتياجاتك القانونية</p>
                        </div>
                        <div class="col-md-4 text-md-end mt-3 mt-md-0">
                            <a href="{{ url_for('consultation') }}" class="btn btn-primary-custom btn-lg rounded-pill px-4">
                                <i class="bi bi-telephone me-2"></i>تواصل معنا
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Latest Articles Section -->
{% if articles %}
<section class="py-5">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold text-primary-custom mb-3">أحدث المقالات</h2>
                <p class="text-muted">آخر المقالات القانونية المنشورة على الموقع</p>
            </div>
        </div>

        <div class="row">
            {% for article in articles %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card h-100 border-0 shadow-sm card-hover">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <span class="badge bg-primary-custom">{{ article.category }}</span>
                            <small class="text-muted">
                                <i class="bi bi-eye me-1"></i>{{ article.views or 0 }}
                            </small>
                        </div>

                        <h6 class="card-title fw-bold">
                            <a href="{{ url_for('article_detail', slug=article.slug) }}"
                               class="text-decoration-none text-dark">
                                {{ article.title_ar }}
                            </a>
                        </h6>

                        <p class="card-text text-muted small">
                            {{ article.excerpt[:100] }}{% if article.excerpt|length > 100 %}...{% endif %}
                        </p>

                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="bi bi-calendar3 me-1"></i>
                                {{ article.published_at }}
                            </small>
                            <a href="{{ url_for('article_detail', slug=article.slug) }}"
                               class="btn btn-sm btn-outline-primary">
                                اقرأ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <div class="text-center mt-4">
            <a href="{{ url_for('articles') }}" class="btn btn-primary-custom btn-lg">
                عرض جميع المقالات
                <i class="bi bi-arrow-left ms-2"></i>
            </a>
        </div>
    </div>
</section>
{% endif %}

<!-- Why Choose Us Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h2 class="fw-bold text-primary-custom mb-3">لماذا تختارنا؟</h2>
                <p class="text-muted">نتميز بالخبرة والمهنية في تقديم الخدمات القانونية</p>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6 mb-3">
                <div class="d-flex align-items-start">
                    <div class="bg-primary-custom text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 50px; height: 50px; flex-shrink: 0;">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold">الموثوقية والدقة</h5>
                        <p class="text-muted">نضمن دقة وموثوقية جميع المعلومات القانونية التي نقدمها مع مراجعة دورية من خبراء قانونيين.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="d-flex align-items-start">
                    <div class="bg-primary-custom text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 50px; height: 50px; flex-shrink: 0;">
                        <i class="bi bi-clock"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold">الاستجابة السريعة</h5>
                        <p class="text-muted">نلتزم بالرد على جميع الاستفسارات والاستشارات خلال 24 ساعة كحد أقصى.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="d-flex align-items-start">
                    <div class="bg-primary-custom text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 50px; height: 50px; flex-shrink: 0;">
                        <i class="bi bi-people"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold">فريق متخصص</h5>
                        <p class="text-muted">فريق من المحامين والخبراء القانونيين المتخصصين في مختلف فروع القانون المغربي.</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="d-flex align-items-start">
                    <div class="bg-primary-custom text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 50px; height: 50px; flex-shrink: 0;">
                        <i class="bi bi-award"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold">التميز والجودة</h5>
                        <p class="text-muted">نسعى للتميز في كل ما نقدمه من خدمات ومحتوى قانوني عالي الجودة.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="py-5 bg-primary-custom text-white">
    <div class="container text-center">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <h2 class="fw-bold mb-3">هل تحتاج إلى مساعدة قانونية؟</h2>
                <p class="lead mb-4">فريقنا من الخبراء القانونيين جاهز لمساعدتك في جميع المسائل القانونية</p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="{{ url_for('consultation') }}" class="btn btn-gold btn-lg px-5 py-3">
                        <i class="bi bi-chat-dots me-2"></i>
                        احصل على استشارة مجانية
                    </a>
                    <a href="tel:{{ site_settings.contact_phone }}" class="btn btn-outline-light btn-lg px-5 py-3">
                        <i class="bi bi-telephone me-2"></i>
                        اتصل بنا الآن
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
    /* Hero Section Styles */
    .hero-section {
        min-height: 100vh;
        position: relative;
        overflow: hidden;
    }

    .hero-bg {
        background: linear-gradient(135deg, #0f172a 0%, #1e3a8a 50%, #1d4ed8 100%);
        background-size: 400% 400%;
        animation: gradientShift 15s ease infinite;
    }

    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    /* Floating Elements */
    .floating-icon {
        position: absolute;
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(5deg); }
    }

    /* Fade In Animation */
    .fade-in {
        opacity: 0;
        transform: translateY(30px);
        animation: fadeIn 0.8s ease-out forwards;
    }

    .fade-in:nth-child(1) { animation-delay: 0.1s; }
    .fade-in:nth-child(2) { animation-delay: 0.2s; }
    .fade-in:nth-child(3) { animation-delay: 0.3s; }
    .fade-in:nth-child(4) { animation-delay: 0.4s; }
    .fade-in:nth-child(5) { animation-delay: 0.5s; }

    @keyframes fadeIn {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Button Hover Effects */
    .btn-hover {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-hover::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-hover:hover::before {
        left: 100%;
    }

    .btn-hover:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    }

    /* Feature Cards */
    .feature-card-modern {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 15px;
        transition: all 0.3s ease;
    }

    .feature-card-modern:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    }

    /* Main Icon Animation */
    .hero-main-icon {
        animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .icon-bg-pulse {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(212, 175, 55, 0.2) 0%, transparent 70%);
        border-radius: 50%;
        animation: pulseGlow 3s ease-in-out infinite;
    }

    @keyframes pulseGlow {
        0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
        50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.8; }
    }

    /* Stats Counter Animation */
    .stats-item {
        transition: all 0.3s ease;
    }

    .stats-item:hover {
        transform: scale(1.1);
    }

    /* Scroll Indicator */
    .scroll-indicator {
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
        40% { transform: translateX(-50%) translateY(-10px); }
        60% { transform: translateX(-50%) translateY(-5px); }
    }

    /* Card Hover Effects */
    .card-hover {
        transition: all 0.3s ease;
    }

    .card-hover:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }

    /* Text Colors */
    .text-white-75 {
        color: rgba(255, 255, 255, 0.75) !important;
    }

    .text-white-50 {
        color: rgba(255, 255, 255, 0.5) !important;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .hero-section {
            min-height: 80vh;
        }

        .display-3 {
            font-size: 2.5rem;
        }

        .floating-icon {
            display: none;
        }

        .feature-card-modern {
            margin-bottom: 1rem;
        }
    }

    /* Section Spacing */
    .section-padding {
        padding: 80px 0;
    }

    /* Enhanced Card Styles */
    .enhanced-card {
        border: none;
        border-radius: 20px;
        overflow: hidden;
        transition: all 0.3s ease;
        background: white;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .enhanced-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .enhanced-card .card-body {
        padding: 2rem;
    }

    /* Service Icons */
    .service-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .service-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s;
    }

    .enhanced-card:hover .service-icon::before {
        transform: translateX(100%);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
// Counter Animation
function animateCounters() {
    const counters = document.querySelectorAll('.counter');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const increment = target / 100;
        let current = 0;

        const updateCounter = () => {
            if (current < target) {
                current += increment;
                counter.textContent = Math.ceil(current);
                setTimeout(updateCounter, 20);
            } else {
                counter.textContent = target;
            }
        };

        updateCounter();
    });
}

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';

            // Trigger counter animation when stats section is visible
            if (entry.target.classList.contains('stats-item')) {
                setTimeout(animateCounters, 500);
            }
        }
    });
}, observerOptions);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Observe fade-in elements
    const fadeElements = document.querySelectorAll('.fade-in');
    fadeElements.forEach(el => observer.observe(el));

    // Observe stats items
    const statsItems = document.querySelectorAll('.stats-item');
    statsItems.forEach(el => observer.observe(el));

    // Smooth scrolling for scroll indicator
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', () => {
            const nextSection = document.querySelector('.section-padding');
            if (nextSection) {
                nextSection.scrollIntoView({ behavior: 'smooth' });
            }
        });
    }

    // Parallax effect for floating elements
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallaxElements = document.querySelectorAll('.floating-icon');

        parallaxElements.forEach((element, index) => {
            const speed = 0.5 + (index * 0.1);
            const yPos = -(scrolled * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    });

    // Add loading animation for cards
    const cards = document.querySelectorAll('.enhanced-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 * index);
    });
});
</script>
{% endblock %}
