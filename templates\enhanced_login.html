{% extends "enhanced_base.html" %}

{% block title %}تسجيل الدخول{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-lg border-0">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-shield-lock text-primary-custom display-4 mb-3"></i>
                        <h3 class="fw-bold text-primary-custom">تسجيل الدخول</h3>
                        <p class="text-muted">لوحة التحكم الإدارية</p>
                    </div>

                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label fw-bold">اسم المستخدم</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-person"></i>
                                </span>
                                <input type="text"
                                       class="form-control"
                                       id="username"
                                       name="username"
                                       required
                                       placeholder="أدخل اسم المستخدم">
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label fw-bold">كلمة المرور</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-lock"></i>
                                </span>
                                <input type="password"
                                       class="form-control"
                                       id="password"
                                       name="password"
                                       required
                                       placeholder="أدخل كلمة المرور">
                                <button class="btn btn-outline-secondary"
                                        type="button"
                                        id="togglePassword">
                                    <i class="bi bi-eye" id="toggleIcon"></i>
                                </button>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox"
                                   class="form-check-input"
                                   id="remember_me"
                                   name="remember_me">
                            <label class="form-check-label" for="remember_me">
                                تذكرني
                            </label>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary-custom btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                تسجيل الدخول
                            </button>
                        </div>
                    </form>

                    <div class="text-center mt-4">
                        <a href="{{ url_for('index') }}" class="text-decoration-none text-muted">
                            <i class="bi bi-arrow-right me-1"></i>
                            العودة إلى الموقع الرئيسي
                        </a>
                    </div>
                </div>
            </div>

            <!-- Demo Credentials -->
            <div class="alert alert-info mt-3" role="alert">
                <i class="bi bi-info-circle me-2"></i>
                <strong>بيانات تجريبية:</strong><br>
                اسم المستخدم: mcmedo36<br>
                كلمة المرور: JA138985kala@!!<br>
                <small class="text-muted">بعد تسجيل الدخول، ادخل على: /medo36</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('toggleIcon');

    if (togglePassword) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // Toggle icon
            if (type === 'password') {
                toggleIcon.classList.remove('bi-eye-slash');
                toggleIcon.classList.add('bi-eye');
            } else {
                toggleIcon.classList.remove('bi-eye');
                toggleIcon.classList.add('bi-eye-slash');
            }
        });
    }

    // Auto-focus on username field
    document.getElementById('username').focus();
});
</script>
{% endblock %}
