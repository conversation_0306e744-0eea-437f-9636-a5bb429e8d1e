<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 80" width="240" height="80">
  <defs>
    <linearGradient id="goldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B8860B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="navyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0f172a;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    <filter id="emboss" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="2" dy="2" result="offset"/>
      <feFlood flood-color="#000000" flood-opacity="0.3"/>
      <feComposite in2="offset" operator="in"/>
    </filter>
  </defs>
  
  <!-- Ornate Border -->
  <rect x="5" y="5" width="230" height="70" rx="15" fill="none" stroke="url(#goldGradient)" stroke-width="2"/>
  <rect x="10" y="10" width="220" height="60" rx="10" fill="none" stroke="url(#goldGradient)" stroke-width="1" opacity="0.5"/>
  
  <!-- Shield Shape -->
  <path d="M 40 15 Q 40 15 50 20 Q 60 15 60 15 L 60 35 Q 60 50 50 55 Q 40 50 40 35 Z" 
        fill="url(#navyGradient)" filter="url(#emboss)"/>
  
  <!-- Crown on Shield -->
  <g transform="translate(50, 30)">
    <!-- Crown base -->
    <rect x="-8" y="0" width="16" height="4" fill="url(#goldGradient)"/>
    
    <!-- Crown points -->
    <polygon points="-8,-8 -4,-12 0,-8 4,-12 8,-8 8,0 -8,0" fill="url(#goldGradient)"/>
    
    <!-- Crown jewels -->
    <circle cx="-4" cy="-6" r="1" fill="#FF6B6B"/>
    <circle cx="0" cy="-4" r="1.5" fill="#4ECDC4"/>
    <circle cx="4" cy="-6" r="1" fill="#FF6B6B"/>
    
    <!-- Scales below -->
    <line x1="-6" y1="8" x2="6" y2="8" stroke="url(#goldGradient)" stroke-width="1"/>
    <circle cx="-4" cy="12" r="2" fill="none" stroke="url(#goldGradient)" stroke-width="1"/>
    <circle cx="4" cy="12" r="2" fill="none" stroke="url(#goldGradient)" stroke-width="1"/>
  </g>
  
  <!-- Elegant Arabic Text -->
  <text x="80" y="35" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="url(#navyGradient)">
    قانوني
  </text>
  <text x="80" y="52" font-family="Arial, sans-serif" font-size="13" fill="url(#navyGradient)" opacity="0.8">
    موقع قانوني متخصص
  </text>
  <text x="80" y="66" font-family="Arial, sans-serif" font-size="10" fill="url(#navyGradient)" opacity="0.6">
    Legal Expert Platform
  </text>
  
  <!-- Decorative Flourishes -->
  <path d="M 75 40 Q 78 38 80 40" stroke="url(#goldGradient)" stroke-width="1" fill="none"/>
  <path d="M 200 40 Q 203 38 205 40" stroke="url(#goldGradient)" stroke-width="1" fill="none"/>
  
  <!-- Corner ornaments -->
  <circle cx="220" cy="20" r="2" fill="url(#goldGradient)" opacity="0.6"/>
  <circle cx="225" cy="30" r="1.5" fill="url(#goldGradient)" opacity="0.4"/>
  <circle cx="215" cy="40" r="1" fill="url(#goldGradient)" opacity="0.3"/>
</svg>
